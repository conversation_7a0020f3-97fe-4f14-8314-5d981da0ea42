import { PrismaClient } from '@prisma/client';

let prisma: PrismaClient | undefined;

const createPrismaClient = async () => {
    if (!prisma) {
        prisma = new PrismaClient();

        try {
            await prisma.$connect();
            console.log('Connected to the database successfully');
        } catch (error: any) {
            console.error('Failed to connect to the database:', error.message);
            throw error;
        }
    }
    return prisma;
};

export default createPrismaClient;
