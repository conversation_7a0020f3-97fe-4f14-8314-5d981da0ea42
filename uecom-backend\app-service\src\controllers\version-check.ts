import { Re<PERSON><PERSON><PERSON><PERSON> } from 'express';
import { versionCheckService } from '../services/version-check';

export const versionCheckController = {
    getAll: (async (_req, res) => {
        try {
            const versionChecks = await versionCheckService.findAll();
            res.status(200).json(versionChecks);
        } catch (err) {
            res.status(500).json({ message: 'Failed to fetch version checks' });
        }
    }) as RequestHandler,

    getByPlatform: (async (req, res) => {
        const { platform } = req.params;
        if (!platform) return res.status(400).json({ message: 'Platform is required' });

        try {
            const versionCheck = await versionCheckService.findByPlatform(platform);
            if (!versionCheck)
                return res.status(404).json({ message: 'Version check not found for this platform' });

            res.status(200).json(versionCheck);
        } catch (err) {
            res.status(500).json({ message: 'Failed to fetch version check' });
        }
    }) as Re<PERSON><PERSON><PERSON><PERSON>,

    getById: (async (req, res) => {
        const id = parseInt(req.params.id, 10);
        if (isNaN(id)) return res.status(400).json({ message: 'Invalid ID' });

        try {
            const versionCheck = await versionCheckService.findById(id);
            if (!versionCheck)
                return res.status(404).json({ message: 'Version check not found' });

            res.status(200).json(versionCheck);
        } catch (err) {
            res.status(500).json({ message: 'Failed to fetch version check' });
        }
    }) as RequestHandler,

    create: (async (req, res) => {
        const {
            platform,
            current_version,
            stable_version,
            download_url,
            stable_message,
            critical_message,
            stable_title,
            critical_title,
            is_playstore_immediate_release,
            version_helper_visibility,
            show_playstore_release,
        } = req.body;

        // Validate required fields
        if (!platform || !current_version || !stable_version || !download_url) {
            return res.status(400).json({
                message: 'Platform, current_version, stable_version, and download_url are required',
            });
        }

        try {
            const versionCheck = await versionCheckService.create(
                platform,
                current_version,
                stable_version,
                download_url,
                stable_message,
                critical_message,
                stable_title,
                critical_title,
                is_playstore_immediate_release,
                version_helper_visibility,
                show_playstore_release
            );
            res.status(201).json(versionCheck);
        } catch (err: any) {
            // Check for unique constraint violation
            if (err.code === 'P2002') {
                return res.status(409).json({ message: 'A version check for this platform already exists' });
            }
            res.status(500).json({ message: 'Failed to create version check' });
        }
    }) as RequestHandler,

    update: (async (req, res) => {
        const id = parseInt(req.params.id, 10);
        if (isNaN(id)) return res.status(400).json({ message: 'Invalid ID' });

        const {
            current_version,
            stable_version,
            download_url,
            stable_message,
            critical_message,
            stable_title,
            critical_title,
            is_playstore_immediate_release,
            version_helper_visibility,
            show_playstore_release,
        } = req.body;

        try {
            const versionCheck = await versionCheckService.update(
                id,
                current_version,
                stable_version,
                download_url,
                stable_message,
                critical_message,
                stable_title,
                critical_title,
                is_playstore_immediate_release,
                version_helper_visibility,
                show_playstore_release
            );
            res.status(200).json(versionCheck);
        } catch (err) {
            res.status(500).json({ message: 'Failed to update version check' });
        }
    }) as RequestHandler,

    delete: (async (req, res) => {
        const id = parseInt(req.params.id, 10);
        if (isNaN(id)) return res.status(400).json({ message: 'Invalid ID' });

        try {
            await versionCheckService.delete(id);
            res.status(204).send();
        } catch (err) {
            res.status(500).json({ message: 'Failed to delete version check' });
        }
    }) as RequestHandler,
};
