import { PrismaClient, version_check } from '@prisma/client';
import createPrismaClient from '../config/prisma';

type VersionCheckCreateInput = {
    platform: string;
    current_version: string;
    stable_version: string;
    stable_message?: string;
    critical_message?: string;
    stable_title?: string;
    critical_title?: string;
    download_url: string;
    is_playstore_immediate_release?: boolean;
    version_helper_visibility?: boolean;
    show_playstore_release?: boolean;
};

type VersionCheckUpdateInput = {
    current_version?: string;
    stable_version?: string;
    stable_message?: string;
    critical_message?: string;
    stable_title?: string;
    critical_title?: string;
    download_url?: string;
    is_playstore_immediate_release?: boolean;
    version_helper_visibility?: boolean;
    show_playstore_release?: boolean;
};

export const versionCheckRepository = {
    getAll: async (): Promise<version_check[]> => {
        const prisma: PrismaClient = await createPrismaClient();
        return prisma.version_check.findMany();
    },

    getByPlatform: async (platform: string): Promise<version_check | null> => {
        const prisma: PrismaClient = await createPrismaClient();
        return prisma.version_check.findUnique({ where: { platform } });
    },

    getById: async (id: number): Promise<version_check | null> => {
        const prisma: PrismaClient = await createPrismaClient();
        return prisma.version_check.findUnique({ where: { id } });
    },

    create: async (data: VersionCheckCreateInput): Promise<version_check> => {
        const prisma: PrismaClient = await createPrismaClient();
        return prisma.version_check.create({ data });
    },

    update: async (id: number, data: VersionCheckUpdateInput): Promise<version_check> => {
        const prisma: PrismaClient = await createPrismaClient();
        return prisma.version_check.update({ where: { id }, data });
    },

    delete: async (id: number): Promise<version_check> => {
        const prisma: PrismaClient = await createPrismaClient();
        return prisma.version_check.delete({ where: { id } });
    },
};
