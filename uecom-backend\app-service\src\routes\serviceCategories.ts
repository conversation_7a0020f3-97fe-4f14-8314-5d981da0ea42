/**
 * Service Categories Routes
 *
 * This file defines the routes for service category operations.
 * It follows the same pattern as the brands routes for consistency.
 */

import { Router } from 'express';
import {
    getAllServiceCategories,
    clearServiceCategoriesCache,
    getServiceCategoriesCacheStatus,
} from '../controllers/serviceCategories';

const router = Router();

/**
 * @route GET /api/service-categories
 * @desc Get all service categories
 * @access Public
 */
router.get('/', getAllServiceCategories);

/**
 * @route DELETE /api/service-categories/cache
 * @desc Clear service categories cache
 * @access Public (should be protected in production)
 */
router.delete('/cache', clearServiceCategoriesCache);

/**
 * @route GET /api/service-categories/cache/status
 * @desc Get cache status
 * @access Public (should be protected in production)
 */
router.get('/cache/status', getServiceCategoriesCacheStatus);

export default router;
