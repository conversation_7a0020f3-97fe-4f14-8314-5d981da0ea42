/**
 * Brands Service
 *
 * This service handles fetching brand data from an external API and provides caching.
 * It follows the Single Responsibility Principle by focusing only on brand data.
 */

import { configService } from '../config/config.service';
import {
    Brand,
    BrandsServiceResponse,
    ExternalBrand,
    ExternalBrandsApiResponse,
} from '../types/brandTypes';
import { logger } from '../utils/logger';

// In-memory cache
interface Cache {
    data: BrandsServiceResponse | null;
    timestamp: number;
}

class BrandsService {
    private cache: Cache = {
        data: null,
        timestamp: 0,
    };

    /**
     * Get all brands
     *
     * @returns Promise resolving to brands data
     */
    async getAllBrands(): Promise<BrandsServiceResponse> {
        try {
            // Check if we have cached data and if it's still valid
            const cacheTtl = configService.getBrandsCacheTtl() * 1000; // Convert to milliseconds
            const now = Date.now();

            // if (this.cache.data && now - this.cache.timestamp < cacheTtl) {
            //     logger.info('Returning cached brands data');
            //     return {
            //         ...this.cache.data,
            //         meta: {
            //             ...this.cache.data.meta,
            //             cached: true,
            //             cachedAt: new Date(this.cache.timestamp).toISOString(),
            //         },
            //     };
            // }

            // Fetch fresh data from external API
            logger.info('Fetching brands data from external API');
            const brandsData = await this.fetchBrandsFromExternalApi();

            // Update cache
            this.cache = {
                data: brandsData,
                timestamp: now,
            };

            return {
                ...brandsData,
                meta: {
                    ...brandsData.meta,
                    cached: false,
                },
            };
        } catch (error) {
            logger.error('Error fetching brands data:', error);

            // If we have cached data, return it even if expired
            if (this.cache.data) {
                logger.info('Returning stale cached brands data due to error');
                return {
                    ...this.cache.data,
                    meta: {
                        ...this.cache.data.meta,
                        cached: true,
                        cachedAt: new Date(this.cache.timestamp).toISOString(),
                    },
                };
            }

            // Return empty response if no cached data
            return { brands: [] };
        }
    }

    /**
     * Fetch brands from external API
     *
     * @returns Promise resolving to brands data
     */
    private async fetchBrandsFromExternalApi(): Promise<BrandsServiceResponse> {
        try {
            const apiUrl = configService.getBrandsApiUrl();
            const apiKey = configService.getBrandsApiKey();

            if (!apiUrl) {
                logger.warn('BRANDS_API_URL not set, using mock data');
                return this.getMockBrandsData();
            }

            // Make request to external API
            const headers: HeadersInit = {
                'Content-Type': 'application/json',
            };

            // Only add Authorization header if API key is provided
            if (apiKey) {
                headers.Authorization = `Bearer ${apiKey}`;
            }

            logger.info(`Fetching brands from API URL: ${apiUrl}`);
            const response = await fetch(apiUrl, { headers });

            if (!response.ok) {
                throw new Error(
                    `External API returned ${response.status}: ${response.statusText}`
                );
            }

            // Parse the response as JSON
            const rawData = await response.json();
            logger.info('Raw API response:', JSON.stringify(rawData));

            // Handle different API response formats
            let brandsData: ExternalBrand[] = [];
            let metaData = {};

            // Check if the response has a data property that is an array
            if (rawData.data && Array.isArray(rawData.data)) {
                brandsData = rawData.data;
                metaData = rawData.meta || {};
                logger.info(`Found ${brandsData.length} brands in data array`);
            }
            // Check if the response itself is an array
            else if (Array.isArray(rawData)) {
                brandsData = rawData;
                logger.info(`Found ${brandsData.length} brands in root array`);
            }
            // Check if the response has a brands property that is an array
            else if (rawData.brands && Array.isArray(rawData.brands)) {
                brandsData = rawData.brands;
                metaData = rawData.meta || {};
                logger.info(
                    `Found ${brandsData.length} brands in brands array`
                );
            }
            // Check if the response has a results property that is an array
            else if (rawData.results && Array.isArray(rawData.results)) {
                brandsData = rawData.results;
                metaData = rawData.meta || {};
                logger.info(
                    `Found ${brandsData.length} brands in results array`
                );
            }
            // If we can't find any brands, log an error and return mock data
            else {
                logger.error('Could not find brands in API response:', rawData);
                return this.getMockBrandsData();
            }

            // Log the first brand to see its structure
            if (brandsData.length > 0) {
                logger.info(
                    'First brand from external API:',
                    JSON.stringify(brandsData[0])
                );
            }

            // Map the brands with proper binding to this instance
            const mappedBrands = brandsData.map((brand) =>
                this.mapExternalBrandToInternal(brand)
            );

            // Log the first mapped brand
            if (mappedBrands.length > 0) {
                logger.info(
                    'First mapped brand:',
                    JSON.stringify(mappedBrands[0])
                );
            }

            // Transform external API response to our internal format
            return {
                brands: mappedBrands,
                meta: {
                    ...metaData,
                    cached: false,
                },
            };
        } catch (error) {
            logger.error('Error fetching brands from external API:', error);

            // Fall back to mock data in case of error
            logger.info('Falling back to mock brands data');
            return this.getMockBrandsData();
        }
    }

    /**
     * Map external brand format to internal format
     *
     * @param externalBrand - Brand data from external API
     * @returns Brand data in internal format
     */
    private mapExternalBrandToInternal(externalBrand: ExternalBrand): Brand {
        // Log the raw brand data to debug
        logger.debug('Raw external brand data:', JSON.stringify(externalBrand));

        // Get the logo URL from any available field
        let logoUrl = '';

        // Try all possible logo field names and validate them
        const possibleUrls = [
            externalBrand.logo,
            externalBrand.icon,
            externalBrand.logo_url,
            externalBrand.image,
            externalBrand.imageUrl,
            externalBrand.img,
            externalBrand.thumbnail,
        ];

        // Find the first valid image URL
        for (const url of possibleUrls) {
            if (url && this.isValidImageUrl(url)) {
                logoUrl = url;
                break;
            }
        }

        // If no valid logo/icon found, use a default placeholder
        if (!logoUrl) {
            logoUrl = 'https://static.wify.co.in/images/placeholder-logo.png';
        }

        // Ensure the logo URL is an absolute URL
        if (
            logoUrl &&
            !logoUrl.startsWith('http') &&
            !logoUrl.startsWith('data:')
        ) {
            // If it's a relative path, convert it to an absolute URL
            if (logoUrl.startsWith('assets/')) {
                // Use a public URL that will be accessible from the frontend
                logoUrl = `${process.env.PUBLIC_URL || 'https://static.wify.co.in'}/${logoUrl}`;
            } else {
                // For other relative paths, assume they're from the API's domain
                logoUrl = `https://static.wify.co.in/images/${logoUrl}`;
            }
        }

        // Get the brand ID from any available field
        const brandId =
            externalBrand.brand_id ||
            externalBrand.id ||
            `brand-${Math.random().toString(36).substring(2, 9)}`;

        // Get the brand name from any available field
        const brandName =
            externalBrand.brand_name ||
            externalBrand.name ||
            externalBrand.title ||
            'Unknown Brand';

        // Get the brand description from any available field
        const description =
            externalBrand.description ||
            externalBrand.desc ||
            externalBrand.about ||
            '';

        // Get the brand website from any available field
        const website =
            externalBrand.website_url ||
            externalBrand.url ||
            externalBrand.website ||
            '';

        // Create the brand object
        const brand: Brand = {
            id: brandId,
            label: brandName,
            logoImage: logoUrl,
            description: description,
            website: website,
        };

        // Log the mapped brand
        logger.debug('Mapped brand:', JSON.stringify(brand));

        return brand;
    }

    /**
     * Validate if a URL is a valid image URL
     *
     * @param url - URL to validate
     * @returns True if the URL is valid for images
     */
    private isValidImageUrl(url: string): boolean {
        if (!url || typeof url !== 'string') {
            return false;
        }

        // Check if it's a valid URL
        try {
            new URL(url);
        } catch {
            return false;
        }

        // Reject Google search URLs and other problematic URLs
        const invalidPatterns = [
            'google.com/search',
            'google.com/url',
            'bing.com/search',
            'yahoo.com/search',
            'duckduckgo.com',
            'javascript:',
            'data:text/',
        ];

        const lowerUrl = url.toLowerCase();
        for (const pattern of invalidPatterns) {
            if (lowerUrl.includes(pattern)) {
                logger.debug(`Rejecting invalid URL pattern: ${url}`);
                return false;
            }
        }

        // Check if URL has a valid image extension or is from a known image hosting service
        const validImageExtensions = [
            '.jpg',
            '.jpeg',
            '.png',
            '.gif',
            '.svg',
            '.webp',
            '.bmp',
        ];
        const validImageHosts = [
            'static.wify.co.in',
            'cdn.shopify.com',
            'upload.wikimedia.org',
            'encrypted-tbn',
            'tse1.mm.bing.net',
            'featherlitefurniture.com',
            'hafeleindia.com',
            'hepoindia.com',
            'tessol.in',
            'saint-gobain.co.in',
            'salice.com',
            'cdn-icons-png.flaticon.com',
            'bincrusher.com',
            'designcafe.com',
            'primexnewsnetwork.com',
            'ulcdn.net',
            'd16pnh712pyiwa.cloudfront.net',
        ];

        // Check for valid image extension
        const hasValidExtension = validImageExtensions.some((ext) =>
            lowerUrl.includes(ext)
        );

        // Check for valid image hosting service
        const hasValidHost = validImageHosts.some((host) =>
            lowerUrl.includes(host)
        );

        const isValid = hasValidExtension || hasValidHost;

        if (!isValid) {
            logger.debug(`Rejecting URL - no valid extension or host: ${url}`);
        }

        return isValid;
    }

    /**
     * Get mock brands data for development/fallback
     *
     * @returns Mock brands data
     */
    private getMockBrandsData(): BrandsServiceResponse {
        return {
            brands: [
                {
                    id: '1',
                    label: 'Blomberg',
                    logoImage:
                        'https://static.wify.co.in/images/amazon_favicon.png',
                    description: 'Blomberg appliances',
                },
                {
                    id: '2',
                    label: 'Hettich',
                    logoImage: 'https://static.wify.co.in/images/wify_logo.png',
                    description: 'Hettich furniture fittings',
                },
                {
                    id: '3',
                    label: 'Godrej',
                    logoImage:
                        'https://static.wify.co.in/images/amazon_favicon.png',
                    description: 'Godrej appliances and furniture',
                },
                {
                    id: '4',
                    label: 'Hindware',
                    logoImage:
                        'https://static.wify.co.in/images/amazon_favicon.png',
                    description: 'Hindware bathroom solutions',
                },
                {
                    id: '5',
                    label: 'Panasonic',
                    logoImage: 'https://static.wify.co.in/images/wify_logo.png',
                    description: 'Panasonic electronics',
                },
                {
                    id: '6',
                    label: 'IKEA',
                    logoImage:
                        'https://static.wify.co.in/images/amazon_favicon.png',
                    description: 'IKEA furniture',
                },
            ],
            meta: {
                total: 6,
                page: 1,
                limit: 10,
                cached: false,
            },
        };
    }
}

// Export a singleton instance
export const brandsService = new BrandsService();
