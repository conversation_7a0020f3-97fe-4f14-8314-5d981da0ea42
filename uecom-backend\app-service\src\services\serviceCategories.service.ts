/**
 * Service Categories Service
 *
 * This service handles fetching service category data from an external API and provides caching.
 * It follows the same pattern as the brands service for consistency.
 */

import { configService } from '../config/config.service';
import { logger } from '../utils/logger';

// Service Category interfaces
export interface ServiceCategory {
    id: string;
    label: string;
    description: string;
    image: string;
    iconName: string;
}

export interface ServiceCategoriesServiceResponse {
    categories: ServiceCategory[];
    meta?: {
        total?: number;
        page?: number;
        limit?: number;
        cached?: boolean;
        cachedAt?: string;
    };
}

export interface ExternalServiceCategory {
    icon?: string;
    image?: string;
    brand_id?: string;
    brand_name?: string;
    id?: string;
    name?: string;
    title?: string;
    description?: string;
    logo?: string;
    logo_url?: string;
    imageUrl?: string;
    img?: string;
    thumbnail?: string;
}

export interface ExternalServiceCategoriesApiResponse {
    data?: ExternalServiceCategory[];
    results?: ExternalServiceCategory[];
    categories?: ExternalServiceCategory[];
    brands?: ExternalServiceCategory[];
    meta?: any;
}

// In-memory cache
interface Cache {
    data: ServiceCategoriesServiceResponse | null;
    timestamp: number;
}

class ServiceCategoriesService {
    private cache: Cache = {
        data: null,
        timestamp: 0,
    };

    /**
     * Get all service categories
     *
     * @returns Promise resolving to service categories data
     */
    async getAllServiceCategories(): Promise<ServiceCategoriesServiceResponse> {
        try {
            // Check if we have cached data and if it's still valid
            const cacheTtl =
                configService.getServiceCategoryCacheTtlMinutes() * 60 * 1000; // Convert minutes to milliseconds
            const now = Date.now();

            if (this.cache.data && now - this.cache.timestamp < cacheTtl) {
                logger.info('Returning cached service categories data');
                return {
                    ...this.cache.data,
                    meta: {
                        ...this.cache.data.meta,
                        cached: true,
                        cachedAt: new Date(this.cache.timestamp).toISOString(),
                    },
                };
            }

            // Fetch fresh data from external API
            logger.info('Fetching service categories data from external API');
            const categoriesData =
                await this.fetchServiceCategoriesFromExternalApi();

            // Update cache
            this.cache = {
                data: categoriesData,
                timestamp: now,
            };

            return {
                ...categoriesData,
                meta: {
                    ...categoriesData.meta,
                    cached: false,
                },
            };
        } catch (error) {
            logger.error('Error fetching service categories data:', error);

            // If we have cached data, return it even if expired
            if (this.cache.data) {
                logger.info(
                    'Returning stale cached service categories data due to error'
                );
                return {
                    ...this.cache.data,
                    meta: {
                        ...this.cache.data.meta,
                        cached: true,
                        cachedAt: new Date(this.cache.timestamp).toISOString(),
                    },
                };
            }

            // Return mock data if no cached data
            return this.getMockServiceCategoriesData();
        }
    }

    /**
     * Fetch service categories from external API
     *
     * @returns Promise resolving to service categories data
     */
    private async fetchServiceCategoriesFromExternalApi(): Promise<ServiceCategoriesServiceResponse> {
        try {
            const apiUrl = configService.getServiceCategoryApiUrl();
            const apiKey = configService.getServiceCategoryApiKey();

            if (!apiUrl) {
                logger.warn(
                    'SERVICE_CATEGORY_API_URL not set, using mock data'
                );
                return this.getMockServiceCategoriesData();
            }

            // Make request to external API
            const headers: HeadersInit = {
                'Content-Type': 'application/json',
            };

            // Only add Authorization header if API key is provided
            if (apiKey) {
                headers.Authorization = `Bearer ${apiKey}`;
            }

            logger.info(`Fetching service categories from API URL: ${apiUrl}`);
            const response = await fetch(apiUrl, { headers });

            if (!response.ok) {
                throw new Error(
                    `External API returned ${response.status}: ${response.statusText}`
                );
            }

            // Parse the response as JSON
            const rawData = await response.json();
            logger.info(
                'Raw service categories API response:',
                JSON.stringify(rawData)
            );

            // Handle different API response formats
            let categoriesData: ExternalServiceCategory[] = [];
            let metaData = {};

            // Check if the response has a data property that is an array
            if (rawData.data && Array.isArray(rawData.data)) {
                categoriesData = rawData.data;
                metaData = rawData.meta || {};
                logger.info(
                    `Found ${categoriesData.length} service categories in data array`
                );
            }
            // Check if the response itself is an array
            else if (Array.isArray(rawData)) {
                categoriesData = rawData;
                logger.info(
                    `Found ${categoriesData.length} service categories in root array`
                );
            }
            // Check if the response has a categories property that is an array
            else if (rawData.categories && Array.isArray(rawData.categories)) {
                categoriesData = rawData.categories;
                metaData = rawData.meta || {};
                logger.info(
                    `Found ${categoriesData.length} service categories in categories array`
                );
            }
            // Check if the response has a results property that is an array
            else if (rawData.results && Array.isArray(rawData.results)) {
                categoriesData = rawData.results;
                metaData = rawData.meta || {};
                logger.info(
                    `Found ${categoriesData.length} service categories in results array`
                );
            }
            // Check if the response has a brands property (since API might return brands as categories)
            else if (rawData.brands && Array.isArray(rawData.brands)) {
                categoriesData = rawData.brands;
                metaData = rawData.meta || {};
                logger.info(
                    `Found ${categoriesData.length} service categories in brands array`
                );
            }
            // If we can't find any categories, log an error and return mock data
            else {
                logger.error(
                    'Could not find service categories in API response:',
                    rawData
                );
                return this.getMockServiceCategoriesData();
            }

            // Log the first category to see its structure
            if (categoriesData.length > 0) {
                logger.info(
                    'First service category from external API:',
                    JSON.stringify(categoriesData[0])
                );
            }

            // Map the categories
            const mappedCategories = categoriesData.map((category) =>
                this.mapExternalCategoryToInternal(category)
            );

            // Log the first mapped category
            if (mappedCategories.length > 0) {
                logger.info(
                    'First mapped service category:',
                    JSON.stringify(mappedCategories[0])
                );
            }

            // Transform external API response to our internal format
            return {
                categories: mappedCategories,
                meta: {
                    ...metaData,
                    cached: false,
                },
            };
        } catch (error) {
            logger.error(
                'Error fetching service categories from external API:',
                error
            );

            // Fall back to mock data in case of error
            logger.info('Falling back to mock service categories data');
            return this.getMockServiceCategoriesData();
        }
    }

    /**
     * Map external service category format to internal format
     */
    private mapExternalCategoryToInternal(
        externalCategory: ExternalServiceCategory
    ): ServiceCategory {
        // Get the image URL from any available field
        let imageUrl =
            externalCategory.image ||
            externalCategory.icon ||
            externalCategory.logo ||
            externalCategory.logo_url ||
            externalCategory.imageUrl ||
            externalCategory.img ||
            externalCategory.thumbnail ||
            'https://static.wify.co.in/images/uecom/BPT_installation.svg';

        // Ensure absolute URL
        if (
            imageUrl &&
            !imageUrl.startsWith('http') &&
            !imageUrl.startsWith('data:')
        ) {
            imageUrl = `https://static.wify.co.in/images/uecom/${imageUrl}`;
        }

        // Get the category ID and name
        const categoryId =
            externalCategory.brand_id ||
            externalCategory.id ||
            `category-${Math.random().toString(36).substring(2, 9)}`;
        const categoryName =
            externalCategory.brand_name ||
            externalCategory.name ||
            externalCategory.title ||
            'Unknown Service';

        return {
            id: categoryId,
            label: categoryName,
            description:
                externalCategory.description ||
                `Professional ${categoryName} services`,
            image: imageUrl,
            iconName: this.extractIconNameFromUrl(imageUrl),
        };
    }

    /**
     * Extract icon name from URL
     */
    private extractIconNameFromUrl(iconUrl: string): string {
        try {
            const filename = iconUrl.split('/').pop()?.split('.')[0];
            return filename || 'service';
        } catch {
            return 'service';
        }
    }

    /**
     * Get mock service categories data
     */
    private getMockServiceCategoriesData(): ServiceCategoriesServiceResponse {
        return {
            categories: [
                {
                    id: '1',
                    label: 'Installation',
                    description: 'Professional installation services',
                    image: 'https://static.wify.co.in/images/uecom/BPT_installation.svg',
                    iconName: 'drill',
                },
                {
                    id: '2',
                    label: 'Home Measurement',
                    description: 'Precise home measurement services',
                    image: 'https://static.wify.co.in/images/uecom/BPT_home_measurement.svg',
                    iconName: 'ruler',
                },
                {
                    id: '3',
                    label: 'Demo',
                    description: 'Product demonstration services',
                    image: 'https://static.wify.co.in/images/uecom/BPT_demo.svg',
                    iconName: 'monitor-play',
                },
                {
                    id: '4',
                    label: 'In-Warranty Support',
                    description: 'Support for products under warranty',
                    image: 'https://static.wify.co.in/images/uecom/BPT_warranty.svg',
                    iconName: 'shield',
                },
                {
                    id: '5',
                    label: 'Out Of Warranty Support',
                    description: 'Support for products outside warranty',
                    image: 'https://static.wify.co.in/images/uecom/BPT_warranty.svg',
                    iconName: 'shield-check',
                },
                {
                    id: '6',
                    label: 'Assembly',
                    description: 'Product assembly services',
                    image: 'https://static.wify.co.in/images/uecom/BPT_assembly.svg',
                    iconName: 'hammer',
                },
            ],
            meta: {
                total: 6,
                page: 1,
                limit: 10,
                cached: false,
            },
        };
    }
}

// Export a singleton instance
export const serviceCategoriesService = new ServiceCategoriesService();
