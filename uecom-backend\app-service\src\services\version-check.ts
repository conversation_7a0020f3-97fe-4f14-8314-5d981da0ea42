import { versionCheckRepository } from '../repositories/version-check.repository';

export const versionCheckService = {
    findAll: () => versionCheckRepository.getAll(),

    findByPlatform: (platform: string) => versionCheckRepository.getByPlatform(platform),

    findById: (id: number) => versionCheckRepository.getById(id),

    create: (
        platform: string,
        current_version: string,
        stable_version: string,
        download_url: string,
        stable_message?: string,
        critical_message?: string,
        stable_title?: string,
        critical_title?: string,
        is_playstore_immediate_release?: boolean,
        version_helper_visibility?: boolean,
        show_playstore_release?: boolean
    ) =>
        versionCheckRepository.create({
            platform,
            current_version,
            stable_version,
            download_url,
            stable_message,
            critical_message,
            stable_title,
            critical_title,
            is_playstore_immediate_release,
            version_helper_visibility,
            show_playstore_release,
        }),

    update: (
        id: number,
        current_version?: string,
        stable_version?: string,
        download_url?: string,
        stable_message?: string,
        critical_message?: string,
        stable_title?: string,
        critical_title?: string,
        is_playstore_immediate_release?: boolean,
        version_helper_visibility?: boolean,
        show_playstore_release?: boolean
    ) =>
        versionCheckRepository.update(id, {
            current_version,
            stable_version,
            download_url,
            stable_message,
            critical_message,
            stable_title,
            critical_title,
            is_playstore_immediate_release,
            version_helper_visibility,
            show_playstore_release,
        }),

    delete: (id: number) => versionCheckRepository.delete(id),
};
