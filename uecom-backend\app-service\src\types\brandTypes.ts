/**
 * Brand Types
 *
 * This file defines the types for brand-related data.
 */

/**
 * Brand data structure
 */
export interface Brand {
    id: string;
    label: string;
    logoImage: string;
    description?: string;
    website?: string;
}

/**
 * Response from external brands API
 */
export interface ExternalBrandsApiResponse {
    data: ExternalBrand[];
    meta?: {
        total: number;
        page: number;
        limit: number;
    };
}

/**
 * External brand data structure (from third-party API)
 * All fields are optional since we don't know exactly what the API will return
 */
export interface ExternalBrand {
    icon?: string;
    logo?: string;
    brand_id?: string;
    brand_name?: string;
    description?: string;
    website_url?: string;
    // The API might return data in different formats, so we need to be flexible
    name?: string; // Alternative to brand_name
    id?: string; // Alternative to brand_id
    logo_url?: string; // Alternative to logo
    image?: string; // Alternative to logo/icon
    url?: string; // Alternative to website_url
    // Add any other fields that might be returned by the external API
    [key: string]: any; // Allow any other properties
}

/**
 * Brands service response
 */
export interface BrandsServiceResponse {
    brands: Brand[];
    meta?: {
        total?: number;
        page?: number;
        limit?: number;
        cached: boolean;
        cachedAt?: string;
    };
}
