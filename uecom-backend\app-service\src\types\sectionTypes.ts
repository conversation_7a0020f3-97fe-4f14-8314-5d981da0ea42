/**
 * Section type definitions for page data
 * These types define the structure of the page data sections
 */

export interface BaseSection {
    type: string
    label: string
    value: any
    visible?: boolean // Flag to determine if section should be shown
}

export interface HeaderSection extends BaseSection {
    type: 'header'
    value: {
        location: string
        Image: string
        logoText: string
        navLinks: { href: string; label: string }[] // Dynamic navigation links
        businessLink: string
    }
}

export interface SearchServiceSection extends BaseSection {
    type: 'search-service'
    value: {
        location: string
        title: string
        subheading: string
        searchPlaceholder: string
        backgroundImage: string
        buttonText: string
    }
}

export interface BannerSection extends BaseSection {
    type: 'banner'
    value: {
        banners?: {
            label: string
            subheading: string
            backgroundImage: string
        }[]
        subheading?: string
        buttonText?: string
        buttonLink?: string
        backgroundImage?: string
        features?: {
            label: string
            icon: string
        }[]
    }
}

export interface ServiceCategoriesSection extends BaseSection {
    type: 'service-categories'
    value: {
        categories: {
            label: string
            description: string
            image?: string
            iconName?: string // Name of the icon to use
        }[]
    }
}

export interface BrandListSection extends BaseSection {
    type: 'brand-list'
    value: {
        brands?: {
            label: string
            logoImage: string
        }[]
    }
}

export interface ReviewListSection extends BaseSection {
    type: 'review-list'
    value: {
        reviews?: {
            text: string
            author: string
            rating: number
            date: string
        }[]
    }
}

export interface StatsSection extends BaseSection {
    type: 'stats'
    iconImage: string
    value: {
        stats: {
            label: string
            value: string
        }[]
    }
}

export interface FooterSection extends BaseSection {
    type: 'footer'
    label: string
    value: {
        tagline: string
        address: string
        phone: string
        email: string
        legalTitle: string
        companyTitle: string
        connectTitle: string
        legalLinks: {
            label: string
            url: string
        }[]
        companyLinks: {
            label: string
            url: string
        }[]
        socials: {
            label: string
            url: string
            icon: string
        }[]
        copyright: string
        companyName: string
    }
}

export interface B2BRegistrationSection extends BaseSection {
    type: 'b2b-registration'
    value: {
        subheading: string
        buttonText: string
        buttonLink: string
        features?: {
            label: string
            icon: string
        }[]
    }
}

export interface NavigationTab {
    id: string
    icon: string // Changed from 'any' to 'string' for backend compatibility
    label: string
    path: string
}

export interface NavigationSection extends BaseSection {
    type: 'navigation'
    label: 'Mobile Bottom Navigation'
    value: {
        tabs: NavigationTab[]
    }
}

export type SectionData =
    | HeaderSection
    | BannerSection
    | ServiceCategoriesSection
    | BrandListSection
    | ReviewListSection
    | StatsSection
    | FooterSection
    | SearchServiceSection
    | B2BRegistrationSection
    | NavigationSection

export interface PageData {
    sections: SectionData[]
}

/**
 * Available languages in the application
 * This is a simplified version since i18n is being removed
 */
export type Language = 'en'
