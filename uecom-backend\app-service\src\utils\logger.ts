/**
 * Logger Utility
 *
 * A centralized logging utility that can be configured based on environment.
 * This allows for easy toggling of logs in different environments.
 */

// Environment-based log level
const isDevelopment = process.env.NODE_ENV !== 'production';

/**
 * Logger class with methods for different log levels
 */
class Logger {
    /**
     * Log info level messages (only in development)
     */
    info(...args: any[]): void {
        if (isDevelopment) {
            console.log('[INFO]', ...args);
        }
    }

    /**
     * Log debug level messages (only in development)
     */
    debug(...args: any[]): void {
        if (isDevelopment) {
            console.log('[DEBUG]', ...args);
        }
    }

    /**
     * Log warning messages (in all environments)
     */
    warn(...args: any[]): void {
        console.warn('[WARN]', ...args);
    }

    /**
     * Log error messages (in all environments)
     */
    error(...args: any[]): void {
        console.error('[ERROR]', ...args);
    }
}

// Export a singleton instance
export const logger = new Logger();
