import { Request, Response } from 'express';
import { greet } from '../../src/controllers/app';
import { getGreeting } from '../../src/services/app';

jest.mock('../../src/services/app');

describe('App Controller', () => {
    let mockRequest: Partial<Request>;
    let mockResponse: Partial<Response>;
    let jsonSpy: jest.Mock;

    beforeEach(() => {
        jsonSpy = jest.fn();
        mockRequest = {};
        mockResponse = {
            status: jest.fn().mockReturnThis(),
            json: jsonSpy,
        };
    });

    describe('greet', () => {
        it('should return greeting message with 200 status', () => {
            const mockMessage = 'Hello, World!';
            (getGreeting as jest.Mock).mockReturnValue(mockMessage);

            greet(mockRequest as Request, mockResponse as Response);

            expect(mockResponse.status).toHaveBeenCalledWith(200);
            expect(jsonSpy).toHaveBeenCalledWith({ message: mockMessage });
        });

        it('should handle service response correctly', () => {
            const mockMessage = 'Custom greeting';
            (getGreeting as jest.Mock).mockReturnValue(mockMessage);

            greet(mockRequest as Request, mockResponse as Response);

            expect(getGreeting).toHaveBeenCalled();
            expect(jsonSpy).toHaveBeenCalledWith({ message: mockMessage });
        });
    });
});
