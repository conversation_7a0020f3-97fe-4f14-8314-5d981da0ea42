import { getGreeting } from '../../src/services/app';

describe('App Service', () => {
    describe('getGreeting', () => {
        it('should return the correct greeting message', () => {
            const result = getGreeting();
            expect(result).toBe('Hello from the App Service!');
        });

        it('should return a string', () => {
            const result = getGreeting();
            expect(typeof result).toBe('string');
        });

        it('should not return an empty string', () => {
            const result = getGreeting();
            expect(result.length).toBeGreaterThan(0);
        });

        it('should be consistent across multiple calls', () => {
            const firstCall = getGreeting();
            const secondCall = getGreeting();
            expect(firstCall).toBe(secondCall);
        });
    });
});
