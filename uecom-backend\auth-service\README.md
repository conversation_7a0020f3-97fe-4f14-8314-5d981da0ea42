# Authentication Service

This service handles user authentication via OTP for the WIFY ECOM application.

## Features

- OTP-based authentication via SMS
- JWT token generation and verification
- Rate limiting for security
- Redis-based OTP storage for persistence and security

## Requirements

- Node.js (v14 or higher)
- Redis (v6 or higher)
- MTalkz SMS Gateway API key

## Setup

1. Clone the repository
2. Install dependencies:
    ```
    npm install
    ```
3. Create a `.env` file based on `.env.example`
4. Start Redis:

    ```
    # Using Docker
    docker run -d -p 6379:6379 redis

    # Or use a managed Redis service
    ```

5. Start the service:
    ```
    npm run dev
    ```

## Redis Configuration

This service uses Redis for OTP storage, rate limiting, and SMS throttling. Redis provides several advantages over in-memory storage:

- **Persistence**: Data is preserved even if the service restarts
- **Automatic Expiration**: Redis handles expiration automatically
- **Scalability**: Multiple service instances can share the same Redis instance
- **Security**: Redis provides authentication and encryption options
- **Distributed Rate Limiting**: Consistent rate limiting across multiple instances

### Configuration Options

Configure Redis in the `.env` file:

```
REDIS_URL=redis://localhost:6379
REDIS_OTP_PREFIX=otp:
OTP_EXPIRATION_SECONDS=900
```

For production, use a secure Redis instance with authentication:

```
REDIS_URL=redis://:password@your-redis-host:6379
```

## API Endpoints

### Health Check

```
GET /health
GET /health/detailed
```

The detailed health check endpoint provides comprehensive information about the service's health, including:

- Overall service status (UP, DOWN, DEGRADED)
- Redis connection status and latency
- Memory usage
- CPU information
- Uptime and version information

### Request OTP

```
POST /v1/auth/request-otp
{
  "mobile": "**********"
}
```

### Verify OTP

```
POST /v1/auth/verify-otp
{
  "mobile": "**********",
  "otp": "123456"
}
```

### Refresh Token

```
POST /v1/auth/refresh-token
{
  "refreshToken": "your-refresh-token"
}
```

### Logout

```
POST /v1/auth/logout
```

This endpoint clears the HTTP-only refresh token cookie.

## Security Features

- Redis-based distributed rate limiting for all endpoints
- Stricter rate limiting for authentication endpoints
- Redis-based SMS throttling to prevent abuse
- JWT-based authentication
- Secure HTTP-only cookies for refresh tokens
- Content Security Policy and other security headers
- Redis-based OTP storage with automatic expiration
- Phone number masking in logs
- Input validation for all endpoints
- CSRF protection using double submit cookie pattern
- Request ID tracking for better debugging and tracing
- Standardized error handling with proper error codes
- Configuration validation at startup
- SOLID principles and design patterns for maintainability

## API Versioning

The service supports API versioning through:

- URL path (e.g., `/v1/auth`)
- Custom header (`X-API-Version`)
- Query parameter (`version`)

This allows for backward compatibility as the API evolves.
