/**
 * Protected Route Example
 * 
 * This file demonstrates how to create protected routes using JWT authentication.
 */

import express, { Request, Response } from 'express';
import { authenticateWithCookie } from '../middlewares/cookie-auth.middleware';
import { authenticate } from '../middlewares/auth.middleware';
import { databaseService } from '../services/database.service';
import { buildAPIResponse } from '../utils/apiResponse';
import { StatusCodes } from 'http-status-codes';

const router = express.Router();

/**
 * Protected route using cookie authentication (for web applications)
 * 
 * This route is protected by the authenticateWithCookie middleware,
 * which verifies the session token in the cookie.
 */
router.get('/profile', authenticateWithCookie, async (req: Request, res: Response) => {
    try {
        // The user ID is available in req.user.id after authentication
        const userId = req.user.id;
        
        // Fetch user data from the database
        const user = await databaseService.findUserById(userId);
        
        if (!user) {
            const response = buildAPIResponse(
                StatusCodes.NOT_FOUND,
                'User not found',
                null,
                { code: 'USER_NOT_FOUND' }
            );
            res.status(StatusCodes.NOT_FOUND).json(response);
            return;
        }
        
        // Return user profile data
        const response = buildAPIResponse(
            StatusCodes.OK,
            'User profile retrieved successfully',
            {
                id: user.consumer_id,
                name: user.name,
                email: user.email,
                mobile: user.mobile_no,
                profileImage: user.profile_image,
                isActive: user.is_active,
            }
        );
        res.status(StatusCodes.OK).json(response);
    } catch (error) {
        console.error('Error fetching user profile:', error);
        
        const response = buildAPIResponse(
            StatusCodes.INTERNAL_SERVER_ERROR,
            'Error fetching user profile',
            null,
            { code: 'PROFILE_FETCH_ERROR' }
        );
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json(response);
    }
});

/**
 * Protected API route using JWT token authentication (for API clients)
 * 
 * This route is protected by the authenticate middleware,
 * which verifies the JWT token in the Authorization header.
 */
router.get('/api/user-data', authenticate, async (req: Request, res: Response) => {
    try {
        // The user ID is available in req.user.id after authentication
        const userId = req.user.id;
        
        // Fetch user data from the database
        const user = await databaseService.findUserById(userId);
        
        if (!user) {
            const response = buildAPIResponse(
                StatusCodes.NOT_FOUND,
                'User not found',
                null,
                { code: 'USER_NOT_FOUND' }
            );
            res.status(StatusCodes.NOT_FOUND).json(response);
            return;
        }
        
        // Return user data
        const response = buildAPIResponse(
            StatusCodes.OK,
            'User data retrieved successfully',
            {
                id: user.consumer_id,
                mobile: user.mobile_no,
                isActive: user.is_active,
            }
        );
        res.status(StatusCodes.OK).json(response);
    } catch (error) {
        console.error('Error fetching user data:', error);
        
        const response = buildAPIResponse(
            StatusCodes.INTERNAL_SERVER_ERROR,
            'Error fetching user data',
            null,
            { code: 'USER_DATA_FETCH_ERROR' }
        );
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json(response);
    }
});

export default router;
