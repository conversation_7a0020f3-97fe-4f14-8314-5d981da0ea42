// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Consumer {
  consumer_id   String    @id @default(uuid()) @db.Uuid
  name          String?   @db.VarChar(500)
  email         String?   @db.VarChar(50)
  mobile_no     String    @unique @db.VarChar(10)
  otp           Int?
  otp_exp       DateTime?
  profile_image String?   @db.VarChar(500)
  is_active     Boolean   @default(true)
  org_id        String?   @db.Uuid
  c_ip          String?   @db.VarChar(100)
  c_user_agent  String?   @db.VarChar(500)
  c_time        DateTime?
  u_ip          String?   @db.VarChar(100)
  u_user_agent  String?   @db.VarChar(500)
  u_time        DateTime?

  @@map("cl_tx_consumer")
}

model UserLogins {
  userId       String    @db.Uuid
  loginNo      Int       @default(autoincrement())
  indType      String    @db.VarChar(100)
  userType     String?   @db.VarChar(20)
  hash         String?   @db.VarChar(100)
  lastSeen     DateTime? @default(now())
  indId        String?   @db.VarChar(100)
  c_ip         String?   @db.VarChar(100)
  c_user_agent String?   @db.VarChar(500)
  c_time       DateTime?
  u_ip         String?   @db.VarChar(100)
  u_user_agent String?   @db.VarChar(500)
  u_time       DateTime?

  @@id([userId, loginNo])
  @@map("cl_tx_usr_logins")
}

model UserIdentities {
  dbId         String    @id @default(uuid()) @db.Uuid
  usrId        String?   @db.Uuid
  identityType String?   @db.Uuid
  id           String?
  key          String?
  token        String?
  orgId        String?   @db.Uuid
  c_ip         String?   @db.VarChar(100)
  c_user_agent String?   @db.VarChar(500)
  c_time       DateTime?
  u_ip         String?   @db.VarChar(100)
  u_user_agent String?   @db.VarChar(500)
  u_time       DateTime?

  identityTypeRelation SysCfIdentity? @relation("SysCfIdentityToUserIdentities", fields: [identityType], references: [identityId])

  @@index([usrId])
  @@map("cl_tx_usr_identities")
}

model SysCfIdentity {
  identityId String  @id @default(uuid()) @db.Uuid
  type       String? @db.VarChar(20)

  UserIdentities UserIdentities[] @relation("SysCfIdentityToUserIdentities")

  @@map("sys_cf_identity")
}
