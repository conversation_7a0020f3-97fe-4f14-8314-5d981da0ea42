/**
 * Authentication Strategy Factory
 *
 * This factory creates authentication strategies based on the configured strategy type.
 * It follows the Factory Method pattern to create different authentication strategy implementations.
 *
 * @module factories/auth-strategy-factory
 */

import { IAuthStrategy } from '../interfaces/auth-strategy.interface';
import { OtpAuthStrategy } from '../strategies/otp-auth-strategy';
import { IOtpStorage } from '../interfaces/otp-storage.interface';
import { ISmsAdapter } from '../adapters/sms-adapter.interface';
import { container } from '../di/container';
import { SERVICE_TOKENS } from '../di/tokens';
import { SmsAdapterFactory } from './sms-adapter-factory';

/**
 * Authentication strategy types
 */
export enum AuthStrategyType {
    OTP = 'otp',
    // Add more strategies as needed (e.g., PASSWORD, OAUTH, etc.)
}

/**
 * Authentication strategy factory
 */
export class AuthStrategyFactory {
    /**
     * Creates an authentication strategy based on the strategy type
     *
     * @param {AuthStrategyType} strategyType - The authentication strategy type
     * @returns {IAuthStrategy} The authentication strategy
     */
    static createStrategy(strategyType: AuthStrategyType): IAuthStrategy {
        switch (strategyType) {
            case AuthStrategyType.OTP:
                const otpStorage = container.resolve<IOtpStorage>(
                    SERVICE_TOKENS.OTP_STORAGE
                );
                const smsAdapter = SmsAdapterFactory.getDefaultAdapter();
                return new OtpAuthStrategy(otpStorage, smsAdapter);
            default:
                throw new Error(
                    `Unsupported authentication strategy: ${strategyType}`
                );
        }
    }

    /**
     * Gets the default authentication strategy based on configuration
     *
     * @returns {IAuthStrategy} The default authentication strategy
     */
    static getDefaultStrategy(): IAuthStrategy {
        // In the future, this could read from configuration to determine the strategy
        return this.createStrategy(AuthStrategyType.OTP);
    }
}
