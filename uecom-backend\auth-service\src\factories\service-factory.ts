/**
 * Service Factory
 *
 * This factory creates and configures service instances.
 * It follows the Factory pattern to centralize service creation
 * and configuration.
 *
 * @module factories/service-factory
 */

import { IAuthService } from '../interfaces/auth-service.interface';
import { IAuthStrategy } from '../interfaces/auth-strategy.interface';
import { IOtpStorage } from '../interfaces/otp-storage.interface';
import { getAuthServiceInstance } from '../services/auth';
import { createOtpRepository } from '../repositories/otp.repository';
import { OtpAuthStrategy } from '../strategies/otp-auth-strategy';
import { ISmsAdapter } from '../adapters/sms-adapter.interface';
import { SmsAdapterFactory } from './sms-adapter-factory';
import { initAuthService } from '../services/auth-service-init';

export class ServiceFactory {
    private static otpStorage: IOtpStorage;
    private static smsAdapter: ISmsAdapter;
    private static authStrategy: IAuthStrategy;

    /**
     * Gets or creates an OTP storage instance
     *
     * @returns {IOtpStorage} The OTP storage instance
     */
    static getOtpStorage(): IOtpStorage {
        if (!this.otpStorage) {
            console.log('🔄 Using Redis OTP storage');
            this.otpStorage = createOtpRepository();
        }
        return this.otpStorage;
    }

    /**
     * Gets or creates an SMS adapter instance
     *
     * @returns {ISmsAdapter} The SMS adapter instance
     */
    static getSmsAdapter(): ISmsAdapter {
        if (!this.smsAdapter) {
            this.smsAdapter = SmsAdapterFactory.getDefaultAdapter();
        }
        return this.smsAdapter;
    }

    /**
     * Gets or creates an authentication strategy instance
     *
     * @returns {IAuthStrategy} The authentication strategy instance
     */
    static getAuthStrategy(): IAuthStrategy {
        if (!this.authStrategy) {
            const otpStorage = this.getOtpStorage();
            const smsAdapter = this.getSmsAdapter();
            this.authStrategy = new OtpAuthStrategy(otpStorage, smsAdapter);
        }
        return this.authStrategy;
    }

    /**
     * Gets or creates an authentication service instance
     *
     * @returns {IAuthService} The authentication service instance
     */
    static getAuthService(): IAuthService {
        // Initialize the auth service if it hasn't been initialized yet
        initAuthService();

        // Return the singleton instance
        return getAuthServiceInstance();
    }
}
