/**
 * SMS Adapter Factory
 *
 * This factory creates SMS adapters based on the configured provider.
 * It follows the Factory Method pattern to create different SMS adapter implementations.
 *
 * @module factories/sms-adapter-factory
 */

import { ISmsAdapter } from '../adapters/sms-adapter.interface';
import { MtalkzSmsAdapter } from '../adapters/mtalkz-sms-adapter';
import { IConfigService } from '../interfaces/config-service.interface';
import { container } from '../di/container';
import { SERVICE_TOKENS } from '../di/tokens';

/**
 * SMS provider types
 */
export enum SmsProvider {
    MTALKZ = 'mtalkz',
    // Add more providers as needed
}

/**
 * SMS adapter factory
 */
export class SmsAdapterFactory {
    /**
     * Creates an SMS adapter based on the provider
     *
     * @param {SmsProvider} provider - The SMS provider to use
     * @returns {ISmsAdapter} The SMS adapter
     */
    static createAdapter(provider: SmsProvider): ISmsAdapter {
        const configService = container.resolve<IConfigService>(
            SERVICE_TOKENS.CONFIG_SERVICE
        );

        switch (provider) {
            case SmsProvider.MTALKZ:
                return new MtalkzSmsAdapter(configService);
            default:
                throw new Error(`Unsupported SMS provider: ${provider}`);
        }
    }

    /**
     * Gets the default SMS adapter based on configuration
     *
     * @returns {ISmsAdapter} The default SMS adapter
     */
    static getDefaultAdapter(): ISmsAdapter {
        // In the future, this could read from configuration to determine the provider
        return this.createAdapter(SmsProvider.MTALKZ);
    }
}
