/**
 * Authentication Service Interface
 *
 * This interface defines the contract for the authentication service.
 * It follows the Interface Segregation Principle by defining only the methods
 * needed for authentication operations.
 *
 * @module interfaces/auth-service
 */

import {
    OtpRequest,
    OtpVerificationRequest,
    ServiceResponse,
    OtpServiceResponse,
    VerificationResponse,
} from '../types/auth.types';

export interface IAuthService {
    /**
     * Health check method to verify service status
     *
     * @returns {string} Status message
     */
    healthCheck(): string;

    /**
     * Generates and sends an OTP to the provided mobile number
     *
     * @param {OtpRequest} params - Object containing the mobile number
     * @returns {Promise<ServiceResponse<OtpServiceResponse>>} Response with success status and data or error
     */
    requestOtp(
        params: OtpRequest
    ): Promise<ServiceResponse<OtpServiceResponse>>;

    /**
     * Verifies an OTP submitted by a user
     *
     * @param {OtpVerificationRequest} params - Object containing mobile number and OTP
     * @returns {Promise<ServiceResponse<VerificationResponse>>} Response with verification result
     */
    verifyOtp(
        params: OtpVerificationRequest
    ): Promise<ServiceResponse<VerificationResponse>>;
}
