/**
 * SMS Service Interface
 *
 * This interface defines the contract for any SMS service implementation.
 * It follows the Interface Segregation Principle by defining only the methods
 * needed for sending SMS messages.
 *
 * @module interfaces/sms-service
 */

export interface SmsRequest {
    to: string;
    message: string;
}

export interface ISmsService {
    /**
     * Sends an SMS message to the specified recipient
     *
     * @param {SmsRequest} request - The SMS request containing recipient and message
     * @returns {Promise<boolean>} True if the SMS was sent successfully, false otherwise
     */
    sendSMS(request: SmsRequest): Promise<boolean>;
}
