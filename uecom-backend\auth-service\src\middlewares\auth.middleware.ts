/**
 * Authentication Middleware
 *
 * This middleware verifies JWT tokens in the Authorization header
 * and attaches the user ID to the request object.
 *
 * @module middlewares/auth
 */

import { Request, Response, NextFunction } from 'express';
import { jwtService, TokenType } from '../services/jwt.service';

// Extend Express Request interface to include user property
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
            };
        }
    }
}

/**
 * Middleware to authenticate requests using JWT
 *
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 * @returns {void}
 */
export const authenticate = (
    req: Request,
    res: Response,
    next: NextFunction
): void => {
    try {
        // Get the authorization header
        const authHeader = req.headers.authorization;

        // Check if the authorization header exists and starts with 'Bearer '
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            res.status(401).json({
                statusCode: 401,
                statusMessage: 'Unauthorized',
                message: 'Authentication required',
                error: {
                    code: 'AUTHENTICATION_REQUIRED',
                },
            });
            return;
        }

        // Extract the token from the authorization header
        const token = authHeader.split(' ')[1];

        // Verify the token
        const decoded = jwtService.verifyToken(token, TokenType.ACCESS);

        // Attach the user ID to the request object
        req.user = {
            id: decoded.sub,
        };

        // Continue to the next middleware or route handler
        next();
    } catch (error: any) {
        // Handle token verification errors
        let statusCode = 401;
        let message = 'Invalid authentication token';
        let code = 'INVALID_TOKEN';

        if (error.message === 'Token has expired') {
            message = 'Authentication token has expired';
            code = 'TOKEN_EXPIRED';
        }

        res.status(statusCode).json({
            statusCode,
            statusMessage: 'Unauthorized',
            message,
            error: {
                code,
            },
        });
    }
};
