/**
 * <PERSON><PERSON> Authentication Middleware
 *
 * This middleware verifies JWT tokens in cookies
 * and attaches the user ID to the request object.
 *
 * @module middlewares/cookie-auth
 */

import { Request, Response, NextFunction } from 'express';
import { jwtService, TokenType } from '../services/jwt.service';
import { CookieName } from '../services/cookie.service';
import { logger } from '../utils/logger';

// Extend Express Request interface to include user property
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
            };
        }
    }
}

/**
 * Middleware to authenticate requests using JWT in cookies
 *
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 * @returns {void}
 */
export const authenticateWithCookie = (
    req: Request,
    res: Response,
    next: NextFunction
): void => {
    try {
        // Get the session token from cookies
        const sessionToken = req.cookies[CookieName.SESSION];

        // Check if the session token exists
        if (!sessionToken) {
            res.status(401).json({
                statusCode: 401,
                statusMessage: 'Unauthorized',
                message: 'Authentication required',
                error: {
                    code: 'AUTHENTICATION_REQUIRED',
                },
            });
            return;
        }

        // Verify the token
        const decoded = jwtService.verifyToken(sessionToken, TokenType.SESSION);

        // Attach the user ID to the request object
        req.user = {
            id: decoded.sub,
        };

        // Continue to the next middleware or route handler
        next();
    } catch (error: any) {
        // Handle token verification errors
        let statusCode = 401;
        let message = 'Invalid authentication token';
        let code = 'INVALID_TOKEN';

        if (error.message === 'Token has expired') {
            message = 'Authentication token has expired';
            code = 'TOKEN_EXPIRED';
        }

        logger.warn('Cookie authentication failed', { error: error.message });

        res.status(statusCode).json({
            statusCode,
            statusMessage: 'Unauthorized',
            message,
            error: {
                code,
            },
        });
    }
};

/**
 * Middleware that attempts to authenticate with a cookie but continues even if authentication fails
 * This is useful for routes that can work with or without authentication
 *
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {NextFunction} next - Express next function
 * @returns {void}
 */
export const optionalAuthenticateWithCookie = (
    req: Request,
    res: Response,
    next: NextFunction
): void => {
    try {
        // Get the session token from cookies
        const sessionToken = req.cookies[CookieName.SESSION];

        // If no token, just continue without authentication
        if (!sessionToken) {
            next();
            return;
        }

        // Verify the token
        const decoded = jwtService.verifyToken(sessionToken, TokenType.SESSION);

        // Attach the user ID to the request object
        req.user = {
            id: decoded.sub,
        };

        // Continue to the next middleware or route handler
        next();
    } catch (error) {
        // If token verification fails, just continue without authentication
        logger.debug('Optional cookie authentication failed', { error });
        next();
    }
};
