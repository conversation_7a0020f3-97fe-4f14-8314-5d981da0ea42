/**
 * Cookie Service
 *
 * This service handles cookie operations for authentication.
 * It provides methods for setting and clearing secure cookies.
 *
 * @module services/cookie
 */

import { Response } from 'express';
import { ConfigService } from '../configs/config.service';
import { logger } from '../utils/logger';

// Cookie names
export enum CookieName {
    SESSION = 'session_token',
    REFRESH = 'refresh_token',
}

export class CookieService {
    private readonly configService: ConfigService;

    // Default cookie path
    private readonly DEFAULT_PATH = '/';

    // Session cookie name
    private readonly SESSION_COOKIE_NAME = CookieName.SESSION;

    constructor() {
        this.configService = new ConfigService();
    }

    /**
     * Sets a secure session cookie with the provided token
     *
     * @param {Response} res - Express response object
     * @param {string} token - The JWT token to store in the cookie
     * @returns {void}
     */
    setSessionCookie(res: Response, token: string): void {
        const isProduction = this.configService.getNodeEnv() === 'production';
        const expiryHours = this.configService.getJwtSessionExpiryHours();

        // Cookie options
        const cookieOptions = {
            httpOnly: true, // Not accessible via JavaScript
            secure: isProduction, // Only sent over HTTPS in production
            sameSite: 'strict' as const, // Prevent CSRF
            maxAge: expiryHours * 60 * 60 * 1000, // Convert hours to milliseconds
            path: this.DEFAULT_PATH,
        };

        // Log cookie settings in development mode
        if (!isProduction) {
            logger.debug('Setting session cookie with options:', cookieOptions);
        }

        res.cookie(this.SESSION_COOKIE_NAME, token, cookieOptions);
    }

    /**
     * Clears the session cookie
     *
     * @param {Response} res - Express response object
     * @returns {void}
     */
    clearSessionCookie(res: Response): void {
        const isProduction = this.configService.getNodeEnv() === 'production';

        // Cookie options for clearing
        const cookieOptions = {
            httpOnly: true,
            secure: isProduction,
            sameSite: 'strict' as const,
            path: this.DEFAULT_PATH,
        };

        // Log cookie clearing in development mode
        if (!isProduction) {
            logger.debug(
                'Clearing session cookie with options:',
                cookieOptions
            );
        }

        res.clearCookie(this.SESSION_COOKIE_NAME, cookieOptions);
    }
}

// Export a singleton instance
export const cookieService = new CookieService();
