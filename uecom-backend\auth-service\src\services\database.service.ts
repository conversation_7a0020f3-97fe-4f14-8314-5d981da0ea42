/**
 * Database Service
 *
 * This service provides an interface to interact with the database through Prisma.
 * It handles user operations and login tracking with enhanced ACID properties.
 *
 * @module services/database
 */

import { PrismaClient } from '@prisma/client';
import { ConfigService } from '../configs/config.service';
import { logger } from '../utils/logger';
import {
    TransactionService,
    createTransactionService,
} from './transaction.service';

// Initialize Prisma client as a global variable to prevent multiple instances
let prismaInstance: PrismaClient | null = null;

export class DatabaseService {
    private prisma: PrismaClient | null = null;
    private readonly configService: ConfigService;
    private transactionService: TransactionService | null = null;

    constructor() {
        this.configService = new ConfigService();
    }

    /**
     * Gets the transaction service instance
     * Creates one if it doesn't exist
     *
     * @returns {Promise<TransactionService>} The transaction service
     */
    private async getTransactionService(): Promise<TransactionService> {
        if (!this.transactionService) {
            const prisma = await this.getPrismaClient();
            this.transactionService = createTransactionService(prisma);
        }
        return this.transactionService;
    }

    /**
     * Initializes the Prisma client
     *
     * @returns {Promise<PrismaClient>} The Prisma client instance
     */
    async getPrismaClient(): Promise<PrismaClient> {
        // Use the global instance if available
        if (prismaInstance) {
            return prismaInstance;
        }

        // Otherwise, initialize a new instance
        if (!this.prisma) {
            try {
                // Create a new Prisma client
                prismaInstance = new PrismaClient({
                    datasources: {
                        db: {
                            url: this.configService.getDatabaseUrl(),
                        },
                    },
                    log: ['error', 'warn'],
                });

                // Connect to the database
                await prismaInstance.$connect();
                this.prisma = prismaInstance;
                logger.info('Connected to the database successfully');
            } catch (error) {
                logger.error('Failed to connect to the database:', error);
                throw new Error('Database connection failed');
            }
        }
        return this.prisma;
    }

    /**
     * Finds a user by mobile number
     * Uses a read transaction with appropriate isolation level
     *
     * @param {string} mobileNo - The mobile number to search for
     * @returns {Promise<any>} The user object if found, null otherwise
     */
    async findUserByMobile(mobileNo: string): Promise<any> {
        try {
            // Get the transaction service
            const transactionService = await this.getTransactionService();

            // Use a read transaction with appropriate isolation level
            return await transactionService.executeReadTransaction(
                async (tx) => {
                    const user = await tx.consumer.findUnique({
                        where: { mobile_no: mobileNo },
                    });

                    if (!user) {
                        logger.debug(`User with mobile ${mobileNo} not found`);
                        return null;
                    }

                    logger.debug(`Found user with mobile ${mobileNo}`);
                    return user;
                }
            );
        } catch (error: any) {
            // Enhanced error logging with more context
            logger.error(`Error finding user by mobile: ${mobileNo}`, {
                error: error.message,
                code: error.code,
                stack: error.stack,
            });

            // Implement graceful degradation
            try {
                // Fallback to direct query without transaction
                logger.info(
                    `Attempting fallback query for user with mobile: ${mobileNo}`
                );
                const prisma = await this.getPrismaClient();
                return await prisma.consumer.findUnique({
                    where: { mobile_no: mobileNo },
                });
            } catch (fallbackError: any) {
                logger.error(
                    `Fallback query also failed: ${fallbackError.message}`
                );
                return null;
            }
        }
    }

    /**
     * Finds a user by ID
     * Uses a read transaction with appropriate isolation level
     *
     * @param {string} userId - The user ID to search for
     * @returns {Promise<any>} The user object if found, null otherwise
     */
    async findUserById(userId: string): Promise<any> {
        try {
            // Get the transaction service
            const transactionService = await this.getTransactionService();

            // Use a read transaction with appropriate isolation level
            return await transactionService.executeReadTransaction(
                async (tx) => {
                    const user = await tx.consumer.findUnique({
                        where: { consumer_id: userId },
                    });

                    if (!user) {
                        logger.debug(`User with ID ${userId} not found`);
                        return null;
                    }

                    logger.debug(`Found user with ID ${userId}`);
                    return user;
                }
            );
        } catch (error: any) {
            // Enhanced error logging with more context
            logger.error(`Error finding user by ID: ${userId}`, {
                error: error.message,
                code: error.code,
                stack: error.stack,
            });

            // Implement graceful degradation
            try {
                // Fallback to direct query without transaction
                logger.info(
                    `Attempting fallback query for user with ID: ${userId}`
                );
                const prisma = await this.getPrismaClient();
                return await prisma.consumer.findUnique({
                    where: { consumer_id: userId },
                });
            } catch (fallbackError: any) {
                logger.error(
                    `Fallback query also failed: ${fallbackError.message}`
                );
                return null;
            }
        }
    }

    /**
     * Creates a new user with the given mobile number
     *
     * @param {string} mobileNo - The mobile number
     * @param {string} ip - The client IP address
     * @param {string} userAgent - The client user agent
     * @returns {Promise<any>} The created user object
     */
    async createUser(
        mobileNo: string,
        ip?: string,
        userAgent?: string
    ): Promise<any> {
        try {
            const prisma = await this.getPrismaClient();

            // First check if user already exists (additional safety check)
            const existingUser = await this.findUserByMobile(mobileNo);
            if (existingUser) {
                logger.info(
                    `User with mobile ${mobileNo} already exists, returning existing user`
                );
                return existingUser;
            }

            logger.info(`Creating new user with mobile: ${mobileNo}`);
            return await prisma.consumer.create({
                data: {
                    mobile_no: mobileNo,
                    is_active: true,
                    c_ip: ip || null,
                    c_user_agent: userAgent || null,
                    c_time: new Date(),
                },
            });
        } catch (error: any) {
            logger.error('Error creating user:', error);
            // Check if this is a unique constraint violation (user already exists)
            if (
                error.code === 'P2002' &&
                error.meta?.target?.includes('mobile_no')
            ) {
                logger.info(
                    `User with mobile ${mobileNo} already exists (caught in constraint error)`
                );
                // Try to fetch the user again
                const user = await this.findUserByMobile(mobileNo);
                if (user) {
                    return user;
                }
            }
            throw new Error('Failed to create user');
        }
    }

    /**
     * Finds or creates a user with the given mobile number
     * Uses a transaction to ensure ACID properties with enhanced error handling
     * and deadlock prevention
     *
     * @param {string} mobileNo - The mobile number
     * @param {string} ip - The client IP address
     * @param {string} userAgent - The client user agent
     * @returns {Promise<any>} The user object
     */
    async findOrCreateUser(
        mobileNo: string,
        ip?: string,
        userAgent?: string
    ): Promise<any> {
        try {
            logger.info(`Finding or creating user with mobile: ${mobileNo}`);

            // Get the transaction service
            const transactionService = await this.getTransactionService();

            // Use the transaction service with serializable isolation
            // This provides automatic retry for deadlocks and connection issues
            return await transactionService.executeSerializableTransaction(
                async (tx) => {
                    // First try to find the user within the transaction
                    const user = await tx.consumer.findUnique({
                        where: { mobile_no: mobileNo },
                    });

                    if (user) {
                        logger.info(
                            `Found existing user with mobile: ${mobileNo}`
                        );
                        return user;
                    }

                    // If not found, create a new user within the same transaction
                    logger.info(
                        `User not found, creating new user with mobile: ${mobileNo}`
                    );

                    return await tx.consumer.create({
                        data: {
                            mobile_no: mobileNo,
                            is_active: true,
                            c_ip: ip || null,
                            c_user_agent: userAgent || null,
                            c_time: new Date(),
                        },
                    });
                }
            );
        } catch (error: any) {
            logger.error(
                `Error in findOrCreateUser for mobile ${mobileNo}:`,
                error
            );

            // Check if this is a unique constraint violation (user already exists)
            if (
                error.code === 'P2002' &&
                error.meta?.target?.includes('mobile_no')
            ) {
                logger.info(
                    `User with mobile ${mobileNo} already exists (caught in constraint error)`
                );

                // Try to fetch the user again with a read transaction
                try {
                    const transactionService =
                        await this.getTransactionService();
                    return await transactionService.executeReadTransaction(
                        async (tx) => {
                            const user = await tx.consumer.findUnique({
                                where: { mobile_no: mobileNo },
                            });

                            if (user) {
                                return user;
                            }

                            throw new Error(
                                `User with mobile ${mobileNo} not found after constraint error`
                            );
                        }
                    );
                } catch (readError: any) {
                    logger.error(
                        `Failed to read user after constraint error: ${readError.message || readError}`
                    );
                    // Fall through to the general error below
                }
            }

            // Enhanced error handling with more specific error messages
            let errorMessage = `Failed to find or create user with mobile: ${mobileNo}`;

            if (error.code === 'P2024' || error.code === 'P2025') {
                errorMessage = `Database connection error while processing user with mobile: ${mobileNo}`;
            } else if (error.code === 'P2034') {
                errorMessage = `Transaction deadlock detected while processing user with mobile: ${mobileNo}`;
            }

            throw new Error(errorMessage);
        }
    }

    /**
     * Records a login session for a user
     * Uses a transaction to ensure ACID properties with enhanced error handling
     * and graceful degradation
     *
     * @param {string} userId - The user ID
     * @param {string} indType - The identity type (e.g., 'MOBILE')
     * @param {string} indId - The identity ID (e.g., mobile number)
     * @param {string} ip - The client IP address
     * @param {string} userAgent - The client user agent
     * @returns {Promise<any>} The created login record or null if recording fails
     */
    async recordLogin(
        userId: string,
        indType: string,
        indId: string,
        ip?: string,
        userAgent?: string
    ): Promise<any> {
        try {
            logger.info(
                `Recording login for user: ${userId}, identity type: ${indType}`
            );

            // Get the transaction service
            const transactionService = await this.getTransactionService();

            // Use a read-committed transaction (lower isolation level is sufficient for this operation)
            // This provides automatic retry for deadlocks and connection issues
            return await transactionService.executeTransaction(async (tx) => {
                // Create the login record
                const loginRecord = await tx.userLogins.create({
                    data: {
                        userId,
                        indType,
                        indId,
                        userType: 'CONSUMER',
                        lastSeen: new Date(),
                        c_ip: ip || null,
                        c_user_agent: userAgent || null,
                        c_time: new Date(),
                    },
                });

                // Also update the user's last seen timestamp
                await tx.consumer.update({
                    where: { consumer_id: userId },
                    data: {
                        u_time: new Date(),
                        u_ip: ip || undefined,
                        u_user_agent: userAgent || undefined,
                    },
                });

                return loginRecord;
            });
        } catch (error: any) {
            // Enhanced error logging with more context
            logger.error(
                `Error recording login for user ${userId} with identity ${indType}:${indId}`,
                {
                    error: error.message,
                    code: error.code,
                    stack: error.stack,
                    userId,
                    indType,
                    indId,
                }
            );

            // Implement graceful degradation
            // Don't throw here, just log the error and return null
            // This ensures the authentication flow can continue even if login recording fails

            // Try to record a minimal login entry without a transaction if possible
            try {
                logger.info(
                    `Attempting fallback login recording for user: ${userId}`
                );
                const prisma = await this.getPrismaClient();

                // Just create the login record without updating the user
                return await prisma.userLogins.create({
                    data: {
                        userId,
                        indType,
                        indId,
                        userType: 'CONSUMER',
                        lastSeen: new Date(),
                        c_ip: ip || null,
                        c_user_agent: userAgent || null,
                        c_time: new Date(),
                    },
                });
            } catch (fallbackError: any) {
                logger.error(
                    `Fallback login recording also failed: ${fallbackError.message}`
                );
                return null;
            }
        }
    }

    /**
     * Gets or creates an identity type
     * Uses a transaction to ensure ACID properties
     *
     * @param {string} type - The identity type (e.g., 'MOBILE')
     * @returns {Promise<any>} The identity type object
     */
    async getOrCreateIdentityType(type: string): Promise<any> {
        try {
            // Get the transaction service
            const transactionService = await this.getTransactionService();

            // Use a transaction with automatic retry for deadlocks
            return await transactionService.executeTransaction(async (tx) => {
                // Try to find existing identity type within the transaction
                const existingType = await tx.sysCfIdentity.findFirst({
                    where: { type },
                });

                if (existingType) {
                    logger.debug(`Found existing identity type: ${type}`);
                    return existingType;
                }

                // Create new identity type if not found
                logger.info(`Creating new identity type: ${type}`);
                return await tx.sysCfIdentity.create({
                    data: { type },
                });
            });
        } catch (error: any) {
            // Enhanced error logging with more context
            logger.error(`Error getting/creating identity type: ${type}`, {
                error: error.message,
                code: error.code,
                stack: error.stack,
            });

            // Check if this is a unique constraint violation (type already exists)
            if (
                error.code === 'P2002' &&
                error.meta?.target?.includes('type')
            ) {
                logger.info(
                    `Identity type ${type} already exists (caught in constraint error)`
                );

                // Try to fetch it again
                try {
                    const prisma = await this.getPrismaClient();
                    const existingType = await prisma.sysCfIdentity.findFirst({
                        where: { type },
                    });

                    if (existingType) {
                        return existingType;
                    }
                } catch (readError: any) {
                    logger.error(
                        `Failed to read identity type after constraint error: ${readError.message || readError}`
                    );
                }
            }

            throw new Error(`Failed to get/create identity type: ${type}`);
        }
    }

    /**
     * Links an identity to a user
     * Uses a transaction to ensure ACID properties with enhanced error handling
     * and deadlock prevention
     *
     * @param {string} userId - The user ID
     * @param {string} identityTypeId - The identity type ID
     * @param {string} identityId - The identity value (e.g., mobile number)
     * @param {string} ip - The client IP address
     * @param {string} userAgent - The client user agent
     * @returns {Promise<any>} The created identity link or null if linking fails
     */
    async linkUserIdentity(
        userId: string,
        identityTypeId: string,
        identityId: string,
        ip?: string,
        userAgent?: string
    ): Promise<any> {
        try {
            logger.info(
                `Linking identity ${identityId} of type ${identityTypeId} to user ${userId}`
            );

            // Get the transaction service
            const transactionService = await this.getTransactionService();

            // Use a transaction with automatic retry for deadlocks
            return await transactionService.executeTransaction(async (tx) => {
                // Check if identity already exists within the transaction
                const existingIdentity = await tx.userIdentities.findFirst({
                    where: {
                        usrId: userId,
                        identityType: identityTypeId,
                        id: identityId,
                    },
                });

                if (existingIdentity) {
                    logger.info(
                        `Identity ${identityId} already linked to user ${userId}`
                    );
                    return existingIdentity;
                }

                // Create new identity link within the transaction
                logger.info(`Creating new identity link for user ${userId}`);
                return await tx.userIdentities.create({
                    data: {
                        usrId: userId,
                        identityType: identityTypeId,
                        id: identityId,
                        c_ip: ip || null,
                        c_user_agent: userAgent || null,
                        c_time: new Date(),
                    },
                });
            });
        } catch (error: any) {
            // Enhanced error logging with more context
            logger.error(
                `Error linking identity ${identityId} of type ${identityTypeId} to user ${userId}`,
                {
                    error: error.message,
                    code: error.code,
                    stack: error.stack,
                    userId,
                    identityTypeId,
                    identityId,
                }
            );

            // Implement graceful degradation
            // First check if the identity already exists (outside of transaction)
            try {
                logger.info(
                    `Checking if identity ${identityId} already exists for user ${userId}`
                );
                const prisma = await this.getPrismaClient();

                const existingIdentity = await prisma.userIdentities.findFirst({
                    where: {
                        usrId: userId,
                        identityType: identityTypeId,
                        id: identityId,
                    },
                });

                if (existingIdentity) {
                    logger.info(
                        `Found existing identity ${identityId} for user ${userId} in fallback check`
                    );
                    return existingIdentity;
                }

                // If not found and it's a unique constraint violation, we can assume it was created
                if (error.code === 'P2002') {
                    logger.info(
                        `Unique constraint violation suggests identity ${identityId} already exists`
                    );

                    // Try to fetch it again
                    return await prisma.userIdentities.findFirst({
                        where: {
                            usrId: userId,
                            identityType: identityTypeId,
                            id: identityId,
                        },
                    });
                }
            } catch (fallbackError: any) {
                logger.error(
                    `Fallback identity check also failed: ${fallbackError.message}`
                );
            }

            // Don't throw here, just log the error and return null
            // This ensures the authentication flow can continue even if identity linking fails
            return null;
        }
    }

    /**
     * Closes the database connection
     * This should be called when shutting down the application
     *
     * @returns {Promise<void>}
     */
    async disconnect(): Promise<void> {
        if (this.prisma) {
            await this.prisma.$disconnect();
            this.prisma = null;
            prismaInstance = null;
            logger.info('Disconnected from the database');
        }
    }
}

// Export a singleton instance
export const databaseService = new DatabaseService();
