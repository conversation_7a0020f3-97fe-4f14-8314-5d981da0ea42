/**
 * Distributed Transaction Service
 *
 * This service implements the Saga pattern for distributed transactions across multiple services.
 * It ensures ACID properties are maintained for operations that span service boundaries.
 *
 * @module services/distributed-transaction
 */

import { logger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';

// Transaction step type
type TransactionStep<T> = {
  execute: () => Promise<T>;
  compensate: (result?: T) => Promise<void>;
  name: string;
};

// Transaction result type
type TransactionResult<T> = {
  success: boolean;
  results: (T | undefined)[];
  error?: Error;
  completedSteps: number;
};

/**
 * Distributed Transaction Service
 * Implements the Saga pattern for distributed transactions
 */
export class DistributedTransactionService {
  private transactionId: string;

  constructor() {
    this.transactionId = uuidv4();
  }

  /**
   * Executes a distributed transaction with compensation
   * If any step fails, all completed steps are compensated in reverse order
   *
   * @template T - The type of the results
   * @param {TransactionStep<T>[]} steps - The steps to execute
   * @returns {Promise<TransactionResult<T>>} - The result of the transaction
   */
  async executeTransaction<T>(steps: TransactionStep<T>[]): Promise<TransactionResult<T>> {
    logger.info(`Starting distributed transaction ${this.transactionId} with ${steps.length} steps`);
    
    const results: (T | undefined)[] = [];
    let completedSteps = 0;
    
    try {
      // Execute each step in order
      for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        logger.info(`Executing step ${i + 1}/${steps.length}: ${step.name}`);
        
        try {
          const result = await step.execute();
          results.push(result);
          completedSteps++;
          logger.info(`Step ${i + 1}/${steps.length}: ${step.name} completed successfully`);
        } catch (error: any) {
          logger.error(`Step ${i + 1}/${steps.length}: ${step.name} failed: ${error.message}`);
          
          // Compensate all completed steps in reverse order
          await this.compensateSteps(steps, results, completedSteps);
          
          return {
            success: false,
            results,
            error: error instanceof Error ? error : new Error(String(error)),
            completedSteps,
          };
        }
      }
      
      logger.info(`Distributed transaction ${this.transactionId} completed successfully`);
      return {
        success: true,
        results,
        completedSteps,
      };
    } catch (error: any) {
      logger.error(`Unexpected error in distributed transaction ${this.transactionId}: ${error.message}`);
      
      // Compensate all completed steps in reverse order
      await this.compensateSteps(steps, results, completedSteps);
      
      return {
        success: false,
        results,
        error: error instanceof Error ? error : new Error(String(error)),
        completedSteps,
      };
    }
  }
  
  /**
   * Compensates completed steps in reverse order
   *
   * @private
   * @template T - The type of the results
   * @param {TransactionStep<T>[]} steps - All steps in the transaction
   * @param {(T | undefined)[]} results - Results of completed steps
   * @param {number} completedSteps - Number of completed steps
   * @returns {Promise<void>}
   */
  private async compensateSteps<T>(
    steps: TransactionStep<T>[],
    results: (T | undefined)[],
    completedSteps: number
  ): Promise<void> {
    logger.info(`Compensating ${completedSteps} completed steps for transaction ${this.transactionId}`);
    
    // Compensate in reverse order
    for (let i = completedSteps - 1; i >= 0; i--) {
      const step = steps[i];
      const result = results[i];
      
      logger.info(`Compensating step ${i + 1}: ${step.name}`);
      
      try {
        await step.compensate(result);
        logger.info(`Step ${i + 1}: ${step.name} compensated successfully`);
      } catch (error: any) {
        // Log compensation errors but continue with other compensations
        logger.error(`Error compensating step ${i + 1}: ${step.name}: ${error.message}`);
      }
    }
    
    logger.info(`Compensation completed for transaction ${this.transactionId}`);
  }
}

// Factory function to create a distributed transaction service
export const createDistributedTransactionService = (): DistributedTransactionService => {
  return new DistributedTransactionService();
};
