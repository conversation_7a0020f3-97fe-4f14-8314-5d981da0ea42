/**
 * Custom HTTP Service
 *
 * This service provides a wrapper around axios for making HTTP requests.
 * It includes enhanced error handling and logging.
 *
 * @module services/http
 */

import axios, { AxiosRequestConfig, AxiosError } from 'axios';

// HTTP method types
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE';

// List of sensitive parameters that should be redacted in logs
const SENSITIVE_PARAMS = ['apikey', 'key', 'token', 'password', 'secret'];

// Default timeout in milliseconds
const DEFAULT_TIMEOUT = 10000;

export class CustomHttpService {
    /**
     * Makes an HTTP request with the specified method, URL, data, and configuration
     *
     * @template T - The expected response data type
     * @param {HttpMethod} method - The HTTP method to use
     * @param {string} url - The URL to make the request to
     * @param {any} data - The data to send with the request (for POST, PUT)
     * @param {AxiosRequestConfig} config - Additional axios configuration
     * @returns {Promise<T>} - The response data
     * @throws {Error} - If the request fails
     */
    async request<T>(
        method: HttpMethod,
        url: string,
        data?: any,
        config: AxiosRequestConfig = {}
    ): Promise<T> {
        // Prepare request configuration
        const requestConfig = this.prepareRequestConfig(
            method,
            url,
            data,
            config
        );

        try {
            // Log the request (without sensitive data)
            this.logRequest(method, url);

            // Make the request
            const response = await axios.request<T>(requestConfig);

            // Log success
            this.logSuccess(method, url, response.status);

            return response.data;
        } catch (error: any) {
            // Log and handle error
            this.logError(method, url, error);
            throw this.createErrorFromResponse(method, error);
        }
    }

    /**
     * Prepares the request configuration
     *
     * @private
     */
    private prepareRequestConfig(
        method: HttpMethod,
        url: string,
        data?: any,
        config: AxiosRequestConfig = {}
    ): AxiosRequestConfig {
        // Ensure headers object exists
        const headers = config.headers || {};

        // Set timeout if not specified
        const timeout = config.timeout || DEFAULT_TIMEOUT;

        return {
            method,
            url,
            data,
            headers,
            timeout,
            ...config,
        };
    }

    /**
     * Logs the HTTP request
     *
     * @private
     */
    private logRequest(method: HttpMethod, url: string): void {
        console.log(`🔄 Making ${method} request to: ${this.sanitizeUrl(url)}`);
    }

    /**
     * Logs a successful HTTP response
     *
     * @private
     */
    private logSuccess(method: HttpMethod, url: string, status: number): void {
        console.log(
            `✅ ${method} request to ${this.sanitizeUrl(
                url
            )} succeeded with status ${status}`
        );
    }

    /**
     * Logs an HTTP error based on its type
     *
     * @private
     */
    private logError(method: HttpMethod, url: string, error: AxiosError): void {
        const sanitizedUrl = this.sanitizeUrl(url);

        if (error.response) {
            // Server responded with non-2xx status
            console.error(
                `❌ HTTP ${method} request to ${sanitizedUrl} failed with status ${error.response.status}:`,
                error.response.data
            );
        } else if (error.request) {
            // Request made but no response received
            console.error(
                `❌ HTTP ${method} request to ${sanitizedUrl} failed: No response received`,
                error.message
            );
        } else {
            // Error in request setup
            console.error(
                `❌ HTTP ${method} request to ${sanitizedUrl} failed during setup:`,
                error.message
            );
        }
    }

    /**
     * Creates a standardized error from an Axios error
     *
     * @private
     */
    private createErrorFromResponse(
        method: HttpMethod,
        error: AxiosError
    ): Error {
        // Extract error message, handling different response formats
        let errorMessage = error.message;

        if (error.response?.data) {
            const data = error.response.data;
            if (typeof data === 'string') {
                errorMessage = data;
            } else if (typeof data === 'object' && data !== null) {
                // Try to find a message property in the response data
                const dataObj = data as Record<string, any>;
                errorMessage =
                    dataObj.message ||
                    dataObj.error ||
                    dataObj.errorMessage ||
                    JSON.stringify(data);
            }
        }

        return new Error(`HTTP ${method} request failed: ${errorMessage}`);
    }

    /**
     * Sanitizes a URL for logging by removing sensitive information
     *
     * @private
     * @param {string} url - The URL to sanitize
     * @returns {string} - The sanitized URL
     */
    private sanitizeUrl(url: string): string {
        try {
            // Create a URL object to parse the URL
            const parsedUrl = new URL(url);
            const params = parsedUrl.searchParams;

            // Redact sensitive parameters
            SENSITIVE_PARAMS.forEach((param) => {
                if (params.has(param)) {
                    params.set(param, '********');
                }
            });

            // Reconstruct the URL with sanitized parameters
            parsedUrl.search = params.toString();
            return parsedUrl.toString();
        } catch (error) {
            // If URL parsing fails, do basic redaction with regex
            return this.sanitizeUrlWithRegex(url);
        }
    }

    /**
     * Fallback URL sanitization using regex
     *
     * @private
     */
    private sanitizeUrlWithRegex(url: string): string {
        let sanitizedUrl = url;

        // Replace each sensitive parameter
        SENSITIVE_PARAMS.forEach((param) => {
            const pattern = new RegExp(`${param}=([^&]+)`, 'g');
            sanitizedUrl = sanitizedUrl.replace(pattern, `${param}=********`);
        });

        return sanitizedUrl;
    }

    /**
     * Makes a GET request
     *
     * @template T - The expected response data type
     * @param {string} url - The URL to make the request to
     * @param {AxiosRequestConfig} config - Additional axios configuration
     * @returns {Promise<T>} - The response data
     */
    async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
        return this.request<T>('GET', url, undefined, config);
    }

    /**
     * Makes a POST request
     *
     * @template T - The expected response data type
     * @param {string} url - The URL to make the request to
     * @param {any} data - The data to send with the request
     * @param {AxiosRequestConfig} config - Additional axios configuration
     * @returns {Promise<T>} - The response data
     */
    async post<T>(
        url: string,
        data: any,
        config?: AxiosRequestConfig
    ): Promise<T> {
        return this.request<T>('POST', url, data, config);
    }

    /**
     * Makes a PUT request
     *
     * @template T - The expected response data type
     * @param {string} url - The URL to make the request to
     * @param {any} data - The data to send with the request
     * @param {AxiosRequestConfig} config - Additional axios configuration
     * @returns {Promise<T>} - The response data
     */
    async put<T>(
        url: string,
        data: any,
        config?: AxiosRequestConfig
    ): Promise<T> {
        return this.request<T>('PUT', url, data, config);
    }

    /**
     * Makes a DELETE request
     *
     * @template T - The expected response data type
     * @param {string} url - The URL to make the request to
     * @param {AxiosRequestConfig} config - Additional axios configuration
     * @returns {Promise<T>} - The response data
     */
    async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
        return this.request<T>('DELETE', url, undefined, config);
    }
}
