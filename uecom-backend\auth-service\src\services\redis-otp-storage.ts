/**
 * Redis OTP Storage Implementation
 *
 * This class provides a Redis-based implementation of the OTP storage interface.
 * It uses Redis for persistent storage with automatic expiration.
 *
 * @module services/redis-otp-storage
 */

import { IOtpStorage } from '../interfaces/otp-storage.interface';
import { IRedisRepository } from '../repositories/redis.repository';
import { IConfigService } from '../interfaces/config-service.interface';
import { container } from '../di/container';
import { SERVICE_TOKENS } from '../di/tokens';

export class RedisOtpStorage implements IOtpStorage {
    private readonly redisRepository: IRedisRepository;
    private readonly configService: IConfigService;
    private readonly keyPrefix: string;
    private readonly defaultExpirySeconds: number;

    constructor() {
        // Get dependencies from the container
        this.redisRepository = container.resolve<IRedisRepository>(
            SERVICE_TOKENS.REDIS_SERVICE
        );
        this.configService = container.resolve<IConfigService>(
            SERVICE_TOKENS.CONFIG_SERVICE
        );

        this.keyPrefix = this.configService.getRedisOtpPrefix();
        this.defaultExpirySeconds =
            this.configService.getOtpExpirationSeconds();
    }

    /**
     * Generates a Redis key for an OTP
     *
     * @private
     * @param {string} mobile - The mobile number
     * @returns {string} The Redis key
     */
    private getKey(mobile: string): string {
        return `${this.keyPrefix}${mobile}`;
    }

    /**
     * Stores an OTP for a given mobile number with expiration
     *
     * @param {string} mobile - The mobile number
     * @param {string} otp - The OTP to store
     * @param {number} [expiryMinutes] - Number of minutes until the OTP expires
     */
    async storeOtp(
        mobile: string,
        otp: string,
        expiryMinutes?: number
    ): Promise<void> {
        const key = this.getKey(mobile);

        // Calculate expiration time in seconds
        const expirySeconds = expiryMinutes
            ? expiryMinutes * 60
            : this.defaultExpirySeconds;

        try {
            // Check if Redis is connected
            if (!this.redisRepository.isReady()) {
                console.warn(
                    '⚠️ Redis is not connected. OTP storage may not be persistent.'
                );
            }

            // Store OTP in Redis with expiration
            await this.redisRepository.set(key, otp, expirySeconds);
            console.log(
                `✅ OTP stored successfully for ${this.maskMobile(mobile)}`
            );
        } catch (error) {
            console.error('❌ Error storing OTP in Redis:', error);
            throw new Error('Failed to store OTP. Please try again later.');
        }
    }

    /**
     * Masks a mobile number for logging purposes
     *
     * @private
     * @param {string} mobile - The mobile number to mask
     * @returns {string} Masked mobile number (e.g., "******1234")
     */
    private maskMobile(mobile: string): string {
        if (mobile.length <= 4) {
            return '****';
        }
        return '*'.repeat(mobile.length - 4) + mobile.slice(-4);
    }

    /**
     * Retrieves an OTP for a given mobile number if it hasn't expired
     *
     * @param {string} mobile - The mobile number
     * @returns {Promise<string | undefined>} The stored OTP or undefined if not found or expired
     */
    async getOtp(mobile: string): Promise<string | undefined> {
        const key = this.getKey(mobile);

        try {
            // Check if Redis is connected
            if (!this.redisRepository.isReady()) {
                console.warn(
                    '⚠️ Redis is not connected. OTP verification may not work properly.'
                );
            }

            // Get OTP from Redis
            const otp = await this.redisRepository.get(key);

            if (otp) {
                console.log(
                    `✅ OTP retrieved successfully for ${this.maskMobile(mobile)}`
                );
            } else {
                console.log(`⚠️ No OTP found for ${this.maskMobile(mobile)}`);
            }

            return otp || undefined;
        } catch (error) {
            console.error('❌ Error retrieving OTP from Redis:', error);
            // Return undefined to indicate OTP not found or error
            return undefined;
        }
    }

    /**
     * Removes an OTP for a given mobile number
     *
     * @param {string} mobile - The mobile number
     */
    async removeOtp(mobile: string): Promise<void> {
        const key = this.getKey(mobile);

        try {
            // Check if Redis is connected
            if (!this.redisRepository.isReady()) {
                console.warn(
                    '⚠️ Redis is not connected. OTP removal may not work properly.'
                );
            }

            // Delete OTP from Redis
            const result = await this.redisRepository.del(key);

            if (result > 0) {
                console.log(
                    `✅ OTP removed successfully for ${this.maskMobile(mobile)}`
                );
            } else {
                console.log(
                    `⚠️ No OTP found to remove for ${this.maskMobile(mobile)}`
                );
            }
        } catch (error) {
            console.error('❌ Error removing OTP from Redis:', error);
            // We don't throw here as removal failure is not critical for the user flow
        }
    }

    /**
     * Cleans up expired OTPs
     * This method is not needed for Redis as it handles expiration automatically
     */
    cleanupExpiredOtps(): void {
        // Redis handles expiration automatically, so this is a no-op
    }
}

// Export a singleton instance
export const redisOtpStorage = new RedisOtpStorage();
