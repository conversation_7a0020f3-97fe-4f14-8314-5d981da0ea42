/**
 * Redis Store for Express Rate Limit
 *
 * This module provides a Redis-based store implementation for express-rate-limit.
 * It allows rate limiting to be distributed across multiple instances of the application.
 *
 * @module services/redis-rate-limit-store
 */

import { Store, ClientRateLimitInfo } from 'express-rate-limit';
import { IRedisRepository } from '../repositories/redis.repository';
import { container } from '../di/container';
import { SERVICE_TOKENS } from '../di/tokens';

export class RedisStore implements Store {
    private readonly redisRepository: IRedisRepository;
    private readonly keyPrefix: string;
    private windowMs: number = 60 * 1000; // Default: 1 minute

    constructor(redisRepository: IRedisRepository, keyPrefix: string = 'rl:') {
        this.redisRepository = redisRepository;
        this.keyPrefix = keyPrefix;
    }

    /**
     * Initializes the store with options from express-rate-limit
     *
     * @param {Object} options - The options passed to express-rate-limit
     */
    init(options: { windowMs: number }): void {
        this.windowMs = options.windowMs;
    }

    /**
     * Generates a Redis key for rate limiting
     *
     * @private
     * @param {string} key - The base key (usually IP address)
     * @returns {string} The Redis key
     */
    private getKey(key: string): string {
        return `${this.keyPrefix}${key}`;
    }

    /**
     * Increments the hit counter for a key
     *
     * @param {string} key - The key to increment (usually IP address)
     * @returns {Promise<ClientRateLimitInfo>} The hit count and reset time
     */
    async increment(key: string): Promise<ClientRateLimitInfo> {
        const redisKey = this.getKey(key);
        const now = Date.now();
        const resetTime = new Date(now + this.windowMs);

        try {
            // Check if Redis is connected
            if (!this.redisRepository.isReady()) {
                console.warn(
                    '⚠️ Redis is not connected. Rate limiting may not work properly.'
                );
                // Return a default value that won't trigger rate limiting
                return {
                    totalHits: 1,
                    resetTime,
                };
            }

            // Get current data
            const data = await this.redisRepository.get(redisKey);
            let info: { hits: number; resetTime: string };

            if (!data) {
                // First hit, create new entry
                info = {
                    hits: 1,
                    resetTime: resetTime.toISOString(),
                };
            } else {
                // Parse existing data
                const parsed = JSON.parse(data);
                info = {
                    hits: parsed.hits + 1,
                    resetTime: parsed.resetTime,
                };
            }

            // Calculate TTL in seconds (how much time is left until reset)
            const resetTimeDate = new Date(info.resetTime);
            const ttlMs = resetTimeDate.getTime() - now;
            const ttlSeconds = Math.ceil(ttlMs / 1000);

            // Store updated data with TTL
            await this.redisRepository.set(
                redisKey,
                JSON.stringify(info),
                ttlSeconds > 0 ? ttlSeconds : 1 // Minimum 1 second TTL
            );

            return {
                totalHits: info.hits,
                resetTime: new Date(info.resetTime),
            };
        } catch (error) {
            console.error('❌ Error incrementing rate limit counter:', error);
            // Return a default value that won't trigger rate limiting
            return {
                totalHits: 1,
                resetTime,
            };
        }
    }

    /**
     * Decrements the hit counter for a key
     *
     * @param {string} key - The key to decrement (usually IP address)
     * @returns {Promise<void>}
     */
    async decrement(key: string): Promise<void> {
        const redisKey = this.getKey(key);

        try {
            // Check if Redis is connected
            if (!this.redisRepository.isReady()) {
                console.warn(
                    '⚠️ Redis is not connected. Rate limiting may not work properly.'
                );
                return;
            }

            // Get current data
            const data = await this.redisRepository.get(redisKey);
            if (!data) {
                return; // No data to decrement
            }

            // Parse existing data
            const parsed = JSON.parse(data);
            const hits = Math.max(0, parsed.hits - 1); // Ensure hits doesn't go below 0

            if (hits === 0) {
                // If hits is 0, remove the key
                await this.redisRepository.del(redisKey);
            } else {
                // Update with decremented hits
                const info = {
                    hits,
                    resetTime: parsed.resetTime,
                };

                // Calculate TTL in seconds
                const resetTimeDate = new Date(info.resetTime);
                const ttlMs = resetTimeDate.getTime() - Date.now();
                const ttlSeconds = Math.ceil(ttlMs / 1000);

                // Store updated data with TTL
                await this.redisRepository.set(
                    redisKey,
                    JSON.stringify(info),
                    ttlSeconds > 0 ? ttlSeconds : 1 // Minimum 1 second TTL
                );
            }
        } catch (error) {
            console.error('❌ Error decrementing rate limit counter:', error);
        }
    }

    /**
     * Gets the hit count and reset time for a key
     *
     * @param {string} key - The key to get (usually IP address)
     * @returns {Promise<ClientRateLimitInfo | undefined>} The hit count and reset time, or undefined if not found
     */
    async get(key: string): Promise<ClientRateLimitInfo | undefined> {
        const redisKey = this.getKey(key);

        try {
            // Check if Redis is connected
            if (!this.redisRepository.isReady()) {
                console.warn(
                    '⚠️ Redis is not connected. Rate limiting may not work properly.'
                );
                return undefined;
            }

            // Get current data
            const data = await this.redisRepository.get(redisKey);
            if (!data) {
                return undefined;
            }

            // Parse data
            const info = JSON.parse(data);
            return {
                totalHits: info.hits,
                resetTime: new Date(info.resetTime),
            };
        } catch (error) {
            console.error('❌ Error getting rate limit info:', error);
            return undefined;
        }
    }

    /**
     * Resets the hit counter for a key
     *
     * @param {string} key - The key to reset (usually IP address)
     * @returns {Promise<void>}
     */
    async resetKey(key: string): Promise<void> {
        const redisKey = this.getKey(key);

        try {
            // Check if Redis is connected
            if (!this.redisRepository.isReady()) {
                console.warn(
                    '⚠️ Redis is not connected. Rate limit reset may not work properly.'
                );
                return;
            }

            // Delete the key
            await this.redisRepository.del(redisKey);
        } catch (error) {
            console.error('❌ Error resetting rate limit counter:', error);
        }
    }

    /**
     * Resets all hit counters (not implemented for Redis)
     * This would require scanning all keys with the prefix, which could be expensive
     */
    async resetAll(): Promise<void> {
        console.warn(
            '⚠️ resetAll is not implemented for Redis store. Keys will expire automatically.'
        );
    }
}

// Factory function for creating a Redis store for express-rate-limit
export const createRedisStore = (keyPrefix: string = 'rl:'): Store => {
    // Get Redis repository from the DI container
    const redisRepository = container.resolve<IRedisRepository>(
        SERVICE_TOKENS.REDIS_SERVICE
    );
    return new RedisStore(redisRepository, keyPrefix);
};
