/**
 * Redis Rate Limiter Service
 *
 * This service provides rate limiting functionality using Redis for persistence.
 * It can be used to limit various actions like SMS sending, API requests, etc.
 *
 * @module services/redis-rate-limiter
 */

import { IRedisRepository } from '../repositories/redis.repository';
import { container } from '../di/container';
import { SERVICE_TOKENS } from '../di/tokens';

export interface IRateLimiter {
    /**
     * Checks if a key has exceeded its rate limit
     *
     * @param {string} key - The key to check (e.g., phone number, IP address)
     * @param {number} maxAttempts - Maximum number of attempts allowed in the time window
     * @param {number} windowSeconds - Time window in seconds
     * @returns {Promise<boolean>} True if rate limited, false otherwise
     */
    isRateLimited(
        key: string,
        maxAttempts: number,
        windowSeconds: number
    ): Promise<boolean>;

    /**
     * Increments the counter for a key
     *
     * @param {string} key - The key to increment (e.g., phone number, IP address)
     * @param {number} windowSeconds - Time window in seconds
     * @returns {Promise<number>} The new count
     */
    increment(key: string, windowSeconds: number): Promise<number>;

    /**
     * Resets the counter for a key
     *
     * @param {string} key - The key to reset
     * @returns {Promise<void>}
     */
    reset(key: string): Promise<void>;
}

export class RedisRateLimiter implements IRateLimiter {
    private readonly redisRepository: IRedisRepository;
    private readonly keyPrefix: string;

    constructor(
        redisRepository: IRedisRepository,
        keyPrefix: string = 'rate:'
    ) {
        this.redisRepository = redisRepository;
        this.keyPrefix = keyPrefix;
    }

    /**
     * Generates a Redis key for rate limiting
     *
     * @private
     * @param {string} key - The base key
     * @returns {string} The Redis key
     */
    private getKey(key: string): string {
        return `${this.keyPrefix}${key}`;
    }

    /**
     * Checks if a key has exceeded its rate limit
     *
     * @param {string} key - The key to check (e.g., phone number, IP address)
     * @param {number} maxAttempts - Maximum number of attempts allowed in the time window
     * @param {number} windowSeconds - Time window in seconds
     * @returns {Promise<boolean>} True if rate limited, false otherwise
     */
    async isRateLimited(
        key: string,
        maxAttempts: number,
        windowSeconds: number
    ): Promise<boolean> {
        const redisKey = this.getKey(key);

        try {
            // Check if Redis is connected
            if (!this.redisRepository.isReady()) {
                console.warn(
                    '⚠️ Redis is not connected. Rate limiting may not work properly.'
                );
                return false; // Fail open if Redis is not available
            }

            // Get current count
            const count = await this.redisRepository.get(redisKey);

            // If no count exists or it's expired, it's not rate limited
            if (!count) {
                return false;
            }

            // Check if count exceeds limit
            return parseInt(count, 10) >= maxAttempts;
        } catch (error) {
            console.error('❌ Error checking rate limit:', error);
            return false; // Fail open if there's an error
        }
    }

    /**
     * Increments the counter for a key
     *
     * @param {string} key - The key to increment (e.g., phone number, IP address)
     * @param {number} windowSeconds - Time window in seconds
     * @returns {Promise<number>} The new count
     */
    async increment(key: string, windowSeconds: number): Promise<number> {
        const redisKey = this.getKey(key);

        try {
            // Check if Redis is connected
            if (!this.redisRepository.isReady()) {
                console.warn(
                    '⚠️ Redis is not connected. Rate limiting may not work properly.'
                );
                return 1; // Return 1 as if this is the first attempt
            }

            // Get current count
            const currentCount = await this.redisRepository.get(redisKey);

            // If no count exists, set to 1 with expiration
            if (!currentCount) {
                await this.redisRepository.set(redisKey, '1', windowSeconds);
                return 1;
            }

            // Increment count
            const newCount = parseInt(currentCount, 10) + 1;
            await this.redisRepository.set(
                redisKey,
                newCount.toString(),
                windowSeconds
            );

            return newCount;
        } catch (error) {
            console.error('❌ Error incrementing rate limit counter:', error);
            return 1; // Return 1 as if this is the first attempt
        }
    }

    /**
     * Resets the counter for a key
     *
     * @param {string} key - The key to reset
     * @returns {Promise<void>}
     */
    async reset(key: string): Promise<void> {
        const redisKey = this.getKey(key);

        try {
            // Check if Redis is connected
            if (!this.redisRepository.isReady()) {
                console.warn(
                    '⚠️ Redis is not connected. Rate limit reset may not work properly.'
                );
                return;
            }

            // Delete the key
            await this.redisRepository.del(redisKey);
        } catch (error) {
            console.error('❌ Error resetting rate limit counter:', error);
        }
    }
}

// Factory function for creating a Redis rate limiter
export const createRedisRateLimiter = (
    keyPrefix: string = 'rate:'
): IRateLimiter => {
    // Get Redis repository from the DI container
    const redisRepository = container.resolve<IRedisRepository>(
        SERVICE_TOKENS.REDIS_SERVICE
    );
    return new RedisRateLimiter(redisRepository, keyPrefix);
};

// Export a singleton instance for SMS rate limiting
export const smsRateLimiter = createRedisRateLimiter('sms-rate:');
