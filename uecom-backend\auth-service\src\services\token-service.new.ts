/**
 * Token Service
 *
 * This service handles JWT token generation and verification.
 * It provides methods for generating access, refresh, and session tokens.
 *
 * @module services/token
 */

import * as jwt from 'jsonwebtoken';
import { ConfigService } from '../configs/config.service';

// Token types
export enum TokenType {
    ACCESS = 'access',
    REFRESH = 'refresh',
    SESSION = 'session',
}

// Token payload interface
export interface TokenPayload {
    sub: string; // Subject (user ID)
    type: TokenType; // Token type
    jti?: string; // JWT ID (unique identifier for this token)
    [key: string]: any; // Allow additional properties
}

export class TokenService {
    private readonly configService: ConfigService;

    // Token expiration times
    private readonly ACCESS_TOKEN_EXPIRY = '1h'; // 1 hour
    private readonly REFRESH_TOKEN_EXPIRY = '7d'; // 7 days

    constructor() {
        this.configService = new ConfigService();
    }

    /**
     * Generates a unique token ID
     *
     * @private
     * @returns {string} A unique token ID
     */
    private generateTokenId(): string {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    /**
     * Generates a JWT access token for a user
     *
     * @param {string} userId - The user's unique identifier
     * @returns {string} The generated JWT access token
     */
    generateAccessToken(userId: string): string {
        const secret = this.configService.getJwtSecret();

        const payload: TokenPayload = {
            sub: userId,
            type: TokenType.ACCESS,
            jti: this.generateTokenId(),
        };

        // Use a simple approach to avoid TypeScript issues
        return jwt.sign(payload, secret);
    }

    /**
     * Generates a JWT refresh token for a user
     *
     * @param {string} userId - The user's unique identifier
     * @returns {string} The generated JWT refresh token
     */
    generateRefreshToken(userId: string): string {
        const secret = this.configService.getJwtSecret();

        const payload: TokenPayload = {
            sub: userId,
            type: TokenType.REFRESH,
            jti: this.generateTokenId(),
        };

        // Use a simple approach to avoid TypeScript issues
        return jwt.sign(payload, secret);
    }

    /**
     * Generates a JWT session token for a user
     * This token is meant to be stored in a secure HTTP-only cookie
     *
     * @param {string} userId - The user's unique identifier
     * @returns {string} The generated JWT session token
     */
    generateSessionToken(userId: string): string {
        const secret = this.configService.getJwtSecret();
        const expiryHours = this.configService.getJwtSessionExpiryHours();

        const payload: TokenPayload = {
            sub: userId,
            type: TokenType.SESSION,
            jti: this.generateTokenId(),
        };

        // Use a simple approach to avoid TypeScript issues
        return jwt.sign(payload, secret);
    }

    /**
     * Verifies a JWT token and returns the decoded payload
     *
     * @param {string} token - The JWT token to verify
     * @param {TokenType} expectedType - The expected token type
     * @returns {TokenPayload} The decoded token payload
     * @throws {Error} If the token is invalid or has expired
     */
    verifyToken(token: string, expectedType?: TokenType): TokenPayload {
        try {
            const secret = this.configService.getJwtSecret();
            const decoded = jwt.verify(token, secret) as TokenPayload;

            // Check if the token type matches the expected type
            if (expectedType && decoded.type !== expectedType) {
                throw new Error('Invalid token type');
            }

            return decoded;
        } catch (error: any) {
            if (error.name === 'TokenExpiredError') {
                throw new Error('Token has expired');
            }
            throw new Error('Invalid token');
        }
    }
}

// Export a singleton instance
export const tokenService = new TokenService();
