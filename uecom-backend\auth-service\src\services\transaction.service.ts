/**
 * Transaction Service
 *
 * This service provides a centralized way to manage database transactions.
 * It ensures ACID properties are maintained across multiple repository operations.
 *
 * @module services/transaction
 */

import { PrismaClient, Prisma } from '@prisma/client';
import { logger } from '../utils/logger';

// Define transaction options type
type TransactionOptions = {
    isolationLevel?: Prisma.TransactionIsolationLevel;
    maxWait?: number;
    timeout?: number;
};

// Default transaction options
const DEFAULT_TRANSACTION_OPTIONS: TransactionOptions = {
    isolationLevel: Prisma.TransactionIsolationLevel.ReadCommitted, // Default isolation level
    maxWait: 5000, // 5 seconds max wait time
    timeout: 10000, // 10 seconds timeout
};

export class TransactionService {
    private prisma: PrismaClient;

    constructor(prisma: PrismaClient) {
        this.prisma = prisma;
    }

    /**
     * Execute a function within a transaction
     *
     * @template T - The return type of the transaction
     * @param {(tx: Prisma.TransactionClient) => Promise<T>} fn - The function to execute within the transaction
     * @param {TransactionOptions} [options] - Transaction options
     * @returns {Promise<T>} - The result of the transaction
     */
    async executeTransaction<T>(
        fn: (tx: Prisma.TransactionClient) => Promise<T>,
        options: TransactionOptions = DEFAULT_TRANSACTION_OPTIONS
    ): Promise<T> {
        const startTime = Date.now();
        let attempt = 1;
        const maxAttempts = 3;

        while (attempt <= maxAttempts) {
            try {
                logger.debug(
                    `Starting transaction (attempt ${attempt}/${maxAttempts})`
                );

                // Execute the transaction with the provided options
                const result = await this.prisma.$transaction(fn, options);

                const duration = Date.now() - startTime;
                logger.debug(
                    `Transaction completed successfully in ${duration}ms`
                );

                return result;
            } catch (error: any) {
                const duration = Date.now() - startTime;

                // Check if this is a deadlock or lock timeout error
                const isDeadlockError =
                    error.code === 'P2034' || // Prisma deadlock error
                    error.message?.includes('deadlock') ||
                    error.message?.includes('lock timeout');

                // Check if this is a connection error
                const isConnectionError =
                    error.code === 'P2024' || // Connection pool timeout
                    error.code === 'P2025' || // Connection lost
                    error.message?.includes('connection');

                if (
                    (isDeadlockError || isConnectionError) &&
                    attempt < maxAttempts
                ) {
                    // If it's a deadlock or connection error and we haven't reached max attempts,
                    // wait with exponential backoff and retry
                    const backoffTime = Math.min(
                        100 * Math.pow(2, attempt),
                        2000
                    );
                    logger.warn(
                        `Transaction failed due to ${isDeadlockError ? 'deadlock' : 'connection error'} ` +
                            `(attempt ${attempt}/${maxAttempts}). Retrying in ${backoffTime}ms. Error: ${error.message}`
                    );

                    // Wait before retrying
                    await new Promise((resolve) =>
                        setTimeout(resolve, backoffTime)
                    );
                    attempt++;
                } else {
                    // For other errors or if we've reached max attempts, log and rethrow
                    logger.error(
                        `Transaction failed after ${duration}ms (attempt ${attempt}/${maxAttempts}): ${error.message}`,
                        { error, stack: error.stack }
                    );
                    throw error;
                }
            }
        }

        // This should never be reached due to the throw in the else block above,
        // but TypeScript requires a return statement
        throw new Error('Transaction failed after maximum retry attempts');
    }

    /**
     * Execute a read-only function within a transaction
     * This uses a lower isolation level optimized for reads
     *
     * @template T - The return type of the transaction
     * @param {(tx: Prisma.TransactionClient) => Promise<T>} fn - The function to execute within the transaction
     * @returns {Promise<T>} - The result of the transaction
     */
    async executeReadTransaction<T>(
        fn: (tx: Prisma.TransactionClient) => Promise<T>
    ): Promise<T> {
        // Use a lower isolation level for read operations
        const readOptions: TransactionOptions = {
            isolationLevel: Prisma.TransactionIsolationLevel.ReadCommitted,
            maxWait: 2000, // Lower wait time for reads
            timeout: 5000, // Lower timeout for reads
        };

        return this.executeTransaction(fn, readOptions);
    }

    /**
     * Execute a function that requires serializable isolation
     * Use this for operations that must be completely isolated from concurrent transactions
     *
     * @template T - The return type of the transaction
     * @param {(tx: Prisma.TransactionClient) => Promise<T>} fn - The function to execute within the transaction
     * @returns {Promise<T>} - The result of the transaction
     */
    async executeSerializableTransaction<T>(
        fn: (tx: Prisma.TransactionClient) => Promise<T>
    ): Promise<T> {
        // Use serializable isolation for operations requiring the highest isolation
        const serializableOptions: TransactionOptions = {
            isolationLevel: Prisma.TransactionIsolationLevel.Serializable,
            maxWait: 8000, // Higher wait time for serializable transactions
            timeout: 15000, // Higher timeout for serializable transactions
        };

        return this.executeTransaction(fn, serializableOptions);
    }
}

// Factory function to create a transaction service
export const createTransactionService = (
    prisma: PrismaClient
): TransactionService => {
    return new TransactionService(prisma);
};
