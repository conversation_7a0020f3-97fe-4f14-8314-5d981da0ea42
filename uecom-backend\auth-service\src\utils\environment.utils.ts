/**
 * Environment Utilities
 *
 * This module provides utility functions for environment-specific behavior.
 * It centralizes environment checks to ensure consistent behavior across the application.
 *
 * @module utils/environment
 */

import { container } from '../di/container';
import { SERVICE_TOKENS } from '../di/tokens';
import { IConfigService } from '../interfaces/config-service.interface';

/**
 * Environment types
 */
export enum Environment {
    DEVELOPMENT = 'development',
    TEST = 'test',
    STAGING = 'staging',
    PRODUCTION = 'production',
}

/**
 * Static OTP for non-production environments
 */
export const STATIC_OTP = '999999';

/**
 * Environment utility class
 */
export class EnvironmentUtils {
    /**
     * Checks if the current environment is production
     *
     * @returns {boolean} True if the current environment is production
     */
    static isProduction(): boolean {
        return false;
        const configService = container.resolve<IConfigService>(
            SERVICE_TOKENS.CONFIG_SERVICE
        );
        const nodeEnv = configService.getNodeEnv();
        return nodeEnv === Environment.PRODUCTION;
    }

    /**
     * Checks if the current environment is development
     *
     * @returns {boolean} True if the current environment is development
     */
    static isDevelopment(): boolean {
        const configService = container.resolve<IConfigService>(
            SERVICE_TOKENS.CONFIG_SERVICE
        );
        const nodeEnv = configService.getNodeEnv();
        return nodeEnv === Environment.DEVELOPMENT;
    }

    /**
     * Checks if the current environment is test
     *
     * @returns {boolean} True if the current environment is test
     */
    static isTest(): boolean {
        const configService = container.resolve<IConfigService>(
            SERVICE_TOKENS.CONFIG_SERVICE
        );
        const nodeEnv = configService.getNodeEnv();
        return nodeEnv === Environment.TEST;
    }

    /**
     * Checks if the current environment is staging
     *
     * @returns {boolean} True if the current environment is staging
     */
    static isStaging(): boolean {
        const configService = container.resolve<IConfigService>(
            SERVICE_TOKENS.CONFIG_SERVICE
        );
        const nodeEnv = configService.getNodeEnv();
        return nodeEnv === Environment.STAGING;
    }

    /**
     * Gets the current environment
     *
     * @returns {string} The current environment
     */
    static getEnvironment(): string {
        const configService = container.resolve<IConfigService>(
            SERVICE_TOKENS.CONFIG_SERVICE
        );
        return configService.getNodeEnv();
    }

    /**
     * Checks if static OTP should be used
     *
     * @returns {boolean} True if static OTP should be used
     */
    static shouldUseStaticOtp(): boolean {
        return !this.isProduction();
    }

    /**
     * Gets the OTP based on the environment
     *
     * @param {string} productionOtp - The OTP to use in production
     * @returns {string} The OTP to use
     */
    static getOtpForEnvironment(productionOtp: string): string {
        if (this.shouldUseStaticOtp()) {
            console.log(
                `🔑 Using static OTP (${STATIC_OTP}) for ${this.getEnvironment()} environment`
            );
            return STATIC_OTP;
        }
        return productionOtp;
    }
}
