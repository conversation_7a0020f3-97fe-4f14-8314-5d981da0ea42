import axios from 'axios';
import { CustomHttpService } from '../../src/services/http';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('CustomHttpService', () => {
    let httpService: CustomHttpService;

    beforeEach(() => {
        httpService = new CustomHttpService();
        jest.clearAllMocks();
    });

    describe('request method', () => {
        it('should make successful request', async () => {
            const mockResponse = { data: { message: 'success' } };
            mockedAxios.request.mockResolvedValueOnce(mockResponse);

            const result = await httpService.request(
                'GET',
                'https://api.example.com'
            );

            expect(result).toEqual(mockResponse.data);
            expect(mockedAxios.request).toHaveBeenCalledWith({
                method: 'GET',
                url: 'https://api.example.com',
                headers: {},
            });
        });

        it('should handle request error', async () => {
            const errorMessage = 'Network Error';
            mockedAxios.request.mockRejectedValueOnce(new Error(errorMessage));

            await expect(
                httpService.request('GET', 'https://api.example.com')
            ).rejects.toThrow(`HTTP GET request failed: ${errorMessage}`);
        });

        it('should handle error with response data', async () => {
            const errorResponse = {
                response: {
                    data: {
                        message: 'Invalid request',
                    },
                },
            };
            mockedAxios.request.mockRejectedValueOnce(errorResponse);

            await expect(
                httpService.request('POST', 'https://api.example.com')
            ).rejects.toThrow('HTTP POST request failed: Invalid request');
        });
    });

    describe('GET method', () => {
        it('should make GET request successfully', async () => {
            const mockData = { data: 'test' };
            mockedAxios.request.mockResolvedValueOnce({ data: mockData });

            const result = await httpService.get('https://api.example.com');

            expect(result).toEqual(mockData);
            expect(mockedAxios.request).toHaveBeenCalledWith({
                method: 'GET',
                url: 'https://api.example.com',
                headers: {},
            });
        });

        it('should make GET request with custom config', async () => {
            const config = { headers: { Authorization: 'Bearer token' } };
            mockedAxios.request.mockResolvedValueOnce({ data: {} });

            await httpService.get('https://api.example.com', config);

            expect(mockedAxios.request).toHaveBeenCalledWith({
                method: 'GET',
                url: 'https://api.example.com',
                ...config,
            });
        });
    });

    describe('POST method', () => {
        it('should make POST request successfully', async () => {
            const postData = { name: 'test' };
            const mockResponse = { id: 1, ...postData };
            mockedAxios.request.mockResolvedValueOnce({ data: mockResponse });

            const result = await httpService.post(
                'https://api.example.com',
                postData
            );

            expect(result).toEqual(mockResponse);
            expect(mockedAxios.request).toHaveBeenCalledWith({
                method: 'POST',
                url: 'https://api.example.com',
                data: postData,
                headers: {},
            });
        });
    });

    describe('PUT method', () => {
        it('should make PUT request successfully', async () => {
            const putData = { name: 'updated' };
            const mockResponse = { id: 1, ...putData };
            mockedAxios.request.mockResolvedValueOnce({ data: mockResponse });

            const result = await httpService.put(
                'https://api.example.com',
                putData
            );

            expect(result).toEqual(mockResponse);
            expect(mockedAxios.request).toHaveBeenCalledWith({
                method: 'PUT',
                url: 'https://api.example.com',
                data: putData,
                headers: {},
            });
        });
    });

    describe('DELETE method', () => {
        it('should make DELETE request successfully', async () => {
            const mockResponse = { message: 'Deleted' };
            mockedAxios.request.mockResolvedValueOnce({ data: mockResponse });

            const result = await httpService.delete('https://api.example.com');

            expect(result).toEqual(mockResponse);
            expect(mockedAxios.request).toHaveBeenCalledWith({
                method: 'DELETE',
                url: 'https://api.example.com',
                headers: {},
            });
        });
    });
});
