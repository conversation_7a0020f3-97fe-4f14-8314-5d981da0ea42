import nextJest from 'next/jest'

const createJestConfig = nextJest({
    dir: './',
})

const customJestConfig = {
    preset: 'ts-jest',
    testEnvironment: 'jsdom', // Uses jsdom to provide browser-like environment
    setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'], // Setup file to load before tests run
    moduleNameMapper: {
        '^@/(.*)$': '<rootDir>/src/$1', // Support path aliases
        '\\.(css|scss|sass)$': 'identity-obj-proxy', // Mock CSS imports
        '^string-width$': '<rootDir>/node_modules/string-width/dist/index.cjs',
    },
    transform: {
        '^.+\\.(ts|tsx|js|jsx)$': 'ts-jest',
        'node_modules/next/dist/server/stream-utils/node-web-streams-helper.js': 'babel-jest',
    },
    transformIgnorePatterns: ['<rootDir>/node_modules/(?!(string-width)/)'],
    collectCoverage: true,
    collectCoverageFrom: [
        'src/**/*.{ts,tsx}',
        '!src/**/*.d.ts',
        '!src/**/index.ts',
        '!src/**/test-utils/*',
    ],
    coverageThreshold: {
        global: {
            branches: 0,
            functions: 0,
            lines: 0,
            statements: 0,
        },
    },
    globals: {
        TextEncoder: require('util').TextEncoder,
        TextDecoder: require('util').TextDecoder,
    },
}

export default createJestConfig(customJestConfig)
