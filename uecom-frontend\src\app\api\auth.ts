import { doPlainPostCall, setRefreshTokenFunction } from '@/lib/utils/http/http-utils'
import axios from 'axios'

const AUTH_BASE_URL = process.env.NEXT_PUBLIC_ECOM_AUTH_SERVICE_URL

export const requestOTP = async (mobile: string): Promise<any> => {
    const url = `${AUTH_BASE_URL}/request-otp`
    const data = { mobile }
    try {
        let resp = await doPlainPostCall(url, data, {})
        return resp
    } catch (error) {
        throw error
    }
}

export const verifyOTP = async (mobile: string, otp: string): Promise<any> => {
    const url = `${AUTH_BASE_URL}/verify-otp`
    const data = { mobile, otp }
    console.log('Calling verify-otp API:', { url, data })
    try {
        let resp = await doPlainPostCall(url, data, {})
        console.log('verify-otp API response:', resp)
        return resp
    } catch (error) {
        console.error('verify-otp API error:', error)
        throw error
    }
}

export const resendOTP = async (mobile: string): Promise<any> => {
    const url = `${AUTH_BASE_URL}/request-otp`
    const data = { mobile }
    try {
        let resp = await doPlainPostCall(url, data, {})
        return resp
    } catch (error) {
        throw error
    }
}

/**
 * Refresh the access token using a refresh token
 *
 * @param refreshToken - The refresh token to use
 * @returns Promise with the API response containing a new access token
 */
export const refreshAccessToken = async (refreshToken: string): Promise<any> => {
    const url = `${AUTH_BASE_URL}/refresh-token`
    const data = { refreshToken }

    try {
        // Use axios directly to avoid circular dependencies with our interceptors
        const response = await axios.post(url, data, {
            headers: {
                'Content-Type': 'application/json',
            },
        })
        return response.data
    } catch (error) {
        console.error('Token refresh error:', error)
        throw error
    }
}

// Register the refresh token function with the HTTP utilities
// This avoids circular dependencies
setRefreshTokenFunction(refreshAccessToken)

/**
 * Logout the user by invalidating tokens on the server
 *
 * @returns Promise with the API response
 */
export const logoutUser = async (): Promise<any> => {
    const url = `${AUTH_BASE_URL}/logout`

    try {
        // Use axios directly to avoid circular dependencies
        const response = await axios.post(
            url,
            {},
            {
                headers: {
                    'Content-Type': 'application/json',
                },
                withCredentials: true, // Important for cookies
            },
        )
        return response.data
    } catch (error) {
        console.error('Logout error:', error)
        throw error
    }
}
