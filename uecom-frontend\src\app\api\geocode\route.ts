import { NextRequest, NextResponse } from 'next/server'

/**
 * Server-side API route to proxy Google Maps Geocoding API requests
 * This protects your API key from being exposed in client-side code
 */
export async function GET(request: NextRequest) {
    try {
        // Get query parameters from the request URL
        const { searchParams } = new URL(request.url)
        const latlng = searchParams.get('latlng')
        const resultType =
            searchParams.get('result_type') || 'street_address|route|neighborhood|locality'
        const language = searchParams.get('language') || 'en'

        // Validate required parameters
        if (!latlng) {
            return NextResponse.json(
                { error: 'Missing required parameter: latlng' },
                { status: 400 },
            )
        }

        // Get API key from environment variables (server-side only)
        const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
        if (!apiKey) {
            console.error('Google Maps API key is not configured')
            return NextResponse.json({ error: 'Server configuration error' }, { status: 500 })
        }

        // Make the request to Google Maps API
        const response = await fetch(
            `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latlng}&key=${apiKey}&result_type=${resultType}&language=${language}`,
        )

        // Get the response data
        const data = await response.json()

        // Return the response
        return NextResponse.json(data)
    } catch (error) {
        console.error('Error in geocode API route:', error)
        return NextResponse.json({ error: 'Failed to fetch geocoding data' }, { status: 500 })
    }
}
