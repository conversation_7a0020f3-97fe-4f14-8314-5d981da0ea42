import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import SignIn from './page'
import { useRouter } from 'next/navigation'

jest.mock('next/navigation', () => ({
    useRouter: jest.fn(),
}))

describe('SignIn Page', () => {
    let mockRouterPush: jest.Mock
    let alertMock: jest.Mock

    beforeEach(() => {
        mockRouterPush = jest.fn()
        alertMock = jest.fn()
        ;(useRouter as jest.Mock).mockReturnValue({ push: mockRouterPush })
        jest.spyOn(window, 'alert').mockImplementation(alertMock)

        global.fetch = jest.fn()
        localStorage.clear()
    })

    afterEach(() => {
        jest.restoreAllMocks()
    })

    describe('TDD: Test-Driven Development', () => {
        it('renders all necessary elements', () => {
            render(<SignIn />)

            expect(screen.getByText('Continue With Phone')).toBeInTheDocument()
            expect(screen.getByText('We will send OTP')).toBeInTheDocument()
            expect(screen.getByPlaceholderText('Enter phone number')).toBeInTheDocument()
            expect(screen.getByText('Get OTP')).toBeInTheDocument()
        })

        it('allows only numeric input and restricts to 10 digits', () => {
            render(<SignIn />)
            const phoneInput = screen.getByPlaceholderText('Enter phone number') as HTMLInputElement

            fireEvent.change(phoneInput, { target: { value: 'a1b2c3d4e567890' } })

            expect(phoneInput.value).toBe('1234567890')
        })

        it('shows an alert when an invalid phone number is entered', () => {
            render(<SignIn />)
            const phoneInput = screen.getByPlaceholderText('Enter phone number')
            const getOTPButton = screen.getByText('Get OTP')

            fireEvent.change(phoneInput, { target: { value: '12345' } })
            fireEvent.click(getOTPButton)

            expect(alertMock).toHaveBeenCalledWith('Please enter a valid 10-digit phone number.')
        })

        it('stores phone number in localStorage and navigates on valid input', async () => {
            ;(fetch as jest.Mock).mockResolvedValue({
                ok: true,
                json: async () => ({ message: 'OTP sent' }),
            })

            render(<SignIn />)
            const phoneInput = screen.getByPlaceholderText('Enter phone number')
            const getOTPButton = screen.getByText('Get OTP')

            fireEvent.change(phoneInput, { target: { value: '9876543210' } })
            fireEvent.click(getOTPButton)

            await waitFor(() => {
                expect(localStorage.getItem('phone')).toBe('9876543210')
                expect(localStorage.getItem('dialCode')).toBe('+91')
                expect(mockRouterPush).toHaveBeenCalledWith('/auth/otp-verification')
            })
        })

        it('handles API failure gracefully', async () => {
            ;(fetch as jest.Mock).mockResolvedValue({
                ok: false,
                json: async () => ({ error: 'OTP failed' }),
            })

            render(<SignIn />)
            const phoneInput = screen.getByPlaceholderText('Enter phone number')
            const getOTPButton = screen.getByText('Get OTP')

            fireEvent.change(phoneInput, { target: { value: '9876543210' } })
            fireEvent.click(getOTPButton)

            await waitFor(() => {
                expect(alertMock).toHaveBeenCalledWith('OTP failed')
            })
        })

        it('ensures phone number stays persistent in localStorage after rerender', () => {
            localStorage.setItem('phone', '9876543210')

            render(<SignIn />)
            const phoneInput = screen.getByPlaceholderText('Enter phone number') as HTMLInputElement

            expect(phoneInput.value).toBe('9876543210')
        })

        it('ensures image is displayed correctly', () => {
            render(<SignIn />)
            const image = screen.getByAltText('Phone Authentication')

            expect(image).toBeInTheDocument()
        })
    })

    describe('BDD: Behavior-Driven Development', () => {
        test('Given the user types into the phone input, When non-numeric characters are entered, Then only numbers should be accepted', () => {
            render(<SignIn />)
            const phoneInput = screen.getByPlaceholderText('Enter phone number') as HTMLInputElement

            fireEvent.change(phoneInput, { target: { value: 'abc123' } })
            expect(phoneInput.value).toBe('123')

            fireEvent.change(phoneInput, { target: { value: '98765432101' } })
            expect(phoneInput.value).toBe('9876543210')
        })

        test("Given a valid phone number is entered, When the user clicks 'Get OTP', Then localStorage should store the values and navigate to OTP verification", async () => {
            ;(fetch as jest.Mock).mockResolvedValue({
                ok: true,
                json: async () => ({ message: 'OTP sent' }),
            })

            render(<SignIn />)
            const phoneInput = screen.getByPlaceholderText('Enter phone number')
            const getOTPButton = screen.getByText('Get OTP')

            fireEvent.change(phoneInput, { target: { value: '9876543210' } })
            fireEvent.click(getOTPButton)

            await waitFor(() => {
                expect(localStorage.getItem('phone')).toBe('9876543210')
                expect(localStorage.getItem('dialCode')).toBe('+91')
                expect(mockRouterPush).toHaveBeenCalledWith('/auth/otp-verification')
            })
        })

        test("Given the API request fails, When the user clicks 'Get OTP', Then an error message should be shown", async () => {
            ;(fetch as jest.Mock).mockResolvedValue({
                ok: false,
                json: async () => ({ error: 'Server error' }),
            })

            render(<SignIn />)
            const phoneInput = screen.getByPlaceholderText('Enter phone number')
            const getOTPButton = screen.getByText('Get OTP')

            fireEvent.change(phoneInput, { target: { value: '9876543210' } })
            fireEvent.click(getOTPButton)

            await waitFor(() => {
                expect(alertMock).toHaveBeenCalledWith('Server error')
            })
        })
    })
})
