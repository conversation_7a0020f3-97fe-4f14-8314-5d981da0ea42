import { render } from '@testing-library/react'
import '@testing-library/jest-dom'
import Page from './page'
import { layoutService } from '@/services/LayoutService'
import { getDeviceFromHeaders } from '@/lib/utils/serverDevice'

// Mock the imported components and functions
jest.mock('@/components/pages/HomePage', () => {
    return {
        __esModule: true,
        default: jest.fn(() => <div data-testid="home-page">HomePage Component</div>),
    }
})

jest.mock('@/services/LayoutService', () => ({
    layoutService: {
        getPageData: jest.fn(),
    },
}))

jest.mock('@/lib/utils/serverDevice', () => ({
    getDeviceFromHeaders: jest.fn(),
}))

describe('Discovery Page', () => {
    beforeEach(() => {
        jest.clearAllMocks()

        // Setup default mock return values
        ;(layoutService.getPageData as jest.Mock).mockResolvedValue({
            sections: [
                {
                    type: 'header',
                    label: 'Header',
                    value: {
                        location: 'Test Location',
                        Image: 'test-image.png',
                        logoText: 'Test Logo',
                        navLinks: [],
                        businessLink: 'Business',
                    },
                },
            ],
        })
        ;(getDeviceFromHeaders as jest.Mock).mockResolvedValue({
            isMobile: false,
            isTablet: false,
            isDesktop: true,
            deviceType: 'desktop',
        })
    })

    it('fetches page data and device info, then renders HomePage', async () => {
        const { findByTestId } = render(await Page())

        // Verify the page component renders
        expect(await findByTestId('home-page')).toBeInTheDocument()

        // Verify the service calls
        expect(layoutService.getPageData).toHaveBeenCalledWith('home')
        expect(getDeviceFromHeaders).toHaveBeenCalled()
    })

    it('handles errors gracefully when fetching page data fails', async () => {
        // Mock an error in getPageData
        ;(layoutService.getPageData as jest.Mock).mockRejectedValue(
            new Error('Failed to load page data'),
        )

        // This should not throw an error because our LayoutService has error handling
        const { findByTestId } = render(await Page())

        // The page should still render with empty data
        expect(await findByTestId('home-page')).toBeInTheDocument()
    })
})
