import { Suspense } from 'react'
import HomePage from '@/components/pages/HomePage'
import { getDeviceFromHeaders } from '@/lib/utils/serverDevice'
import SectionLoading from '@/components/ui/molecules/SectionLoading'
import { PageData } from '@/types'

/**
 * Fetch page data from the API
 * This function is used to fetch data during server-side rendering
 */
async function getPageData(): Promise<PageData> {
    try {
        console.log('debugging :: 2 :: Fetching page data in discovery page')
        // Use a default URL if the environment variable is not set
        // For server components, we need to use the server-side environment variable
        const baseUrl = process.env.NEXT_PUBLIC_APP_SERVICE_URL || 'http://localhost:8000'
        console.log('Using backend URL:', baseUrl)
        const apiUrl = `${baseUrl}/api/page-data/home`

        console.log(`Fetching page data from: ${apiUrl}`)

        const res = await fetch(apiUrl, {
            // cache: 'no-store', // Don't cache the response
            next: { revalidate: 60 }, // Revalidate every 60 seconds
        })

        if (!res.ok) {
            throw new Error(`Failed to fetch page data: ${res.statusText}`)
        }

        return res.json()
    } catch (error) {
        console.error('Error fetching page data:', error)

        // Return empty page data as fallback
        return { sections: [] }
    }
}

/**
 * Main page component
 * Fetches page data from the API and passes it to the HomePage component
 * This is a Server Component that fetches data and passes it to client components
 */
export default async function Page() {
    // Fetch page data from the API

    console.log('Debugging :: Fetching page data in discovery page')
    const pageData = await getPageData()

    // Get device information from request headers for SSR
    const deviceInfo = await getDeviceFromHeaders()

    return (
        <Suspense fallback={<SectionLoading />}>
            {pageData && deviceInfo && (
                <HomePage pageData={pageData} initialDeviceType={deviceInfo.deviceType} />
            )}
        </Suspense>
    )
}
