'use client'

import React, { useEffect } from 'react'
import { AlertTriangle, RefreshCw } from 'lucide-react'

interface GlobalErrorProps {
    error: Error & { digest?: string }
    reset: () => void
}

/**
 * Global error page component for handling critical application errors
 * This catches errors in the root layout and provides a fallback UI
 */
export default function GlobalError({ error, reset }: GlobalErrorProps) {
    useEffect(() => {
        // Log the critical error to an error reporting service
        console.error('Critical application error:', error)
    }, [error])

    const handleReset = () => {
        // Clear all cached data on critical error
        if (typeof window !== 'undefined') {
            // Clear localStorage and sessionStorage
            localStorage.clear()
            sessionStorage.clear()
            
            // Clear any service worker caches if needed
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name)
                    })
                })
            }
        }
        reset()
    }

    return (
        <html lang="en">
            <body>
                <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
                    <div className="max-w-lg w-full bg-white rounded-lg shadow-lg p-8 text-center">
                        {/* Error Icon */}
                        <div className="flex justify-center mb-6">
                            <div className="bg-red-100 rounded-full p-4">
                                <AlertTriangle className="h-16 w-16 text-red-600" />
                            </div>
                        </div>

                        {/* Error Title */}
                        <h1 className="text-3xl font-bold text-gray-900 mb-4">
                            Critical Error
                        </h1>

                        {/* Error Message */}
                        <p className="text-gray-600 mb-6 text-lg">
                            A critical error occurred that prevented the application from loading properly. 
                            We apologize for the inconvenience.
                        </p>

                        {/* Error Details (only in development) */}
                        {process.env.NODE_ENV === 'development' && (
                            <div className="bg-gray-100 rounded-lg p-4 mb-6 text-left">
                                <h3 className="font-semibold text-gray-900 mb-2">Error Details:</h3>
                                <p className="text-sm text-gray-700 font-mono break-all">
                                    {error.message}
                                </p>
                                {error.digest && (
                                    <p className="text-xs text-gray-500 mt-2">
                                        Error ID: {error.digest}
                                    </p>
                                )}
                                {error.stack && (
                                    <details className="mt-2">
                                        <summary className="text-sm font-semibold text-gray-700 cursor-pointer">
                                            Stack Trace
                                        </summary>
                                        <pre className="text-xs text-gray-600 mt-2 overflow-auto max-h-32">
                                            {error.stack}
                                        </pre>
                                    </details>
                                )}
                            </div>
                        )}

                        {/* Action Button */}
                        <button
                            onClick={handleReset}
                            className="w-full bg-red-600 hover:bg-red-700 text-white font-semibold py-4 px-6 rounded-lg transition-colors flex items-center justify-center gap-3 text-lg"
                        >
                            <RefreshCw className="h-5 w-5" />
                            Restart Application
                        </button>

                        {/* Additional Information */}
                        <div className="mt-8 pt-6 border-t border-gray-200">
                            <p className="text-sm text-gray-500">
                                If this problem continues to occur, please clear your browser cache and cookies, 
                                or try accessing the application in an incognito/private browsing window.
                            </p>
                        </div>

                        {/* Manual Refresh Option */}
                        <div className="mt-4">
                            <button
                                onClick={() => window.location.reload()}
                                className="text-blue-600 hover:text-blue-700 underline text-sm"
                            >
                                Or click here to manually refresh the page
                            </button>
                        </div>
                    </div>
                </div>
            </body>
        </html>
    )
}
