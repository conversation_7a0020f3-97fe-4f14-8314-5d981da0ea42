@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    /* Brand colors */
    --blue-primary: #202e4b;
    --orange-primary: #f76e11;
    --grey-01: #dde3ed;
    --grey-02: #f2f4f8;
    --grey-03: #f5f8fd;
    --grey-04: #f8fafe;
    --white: #ffffff;
    --green: #e5fee2;
    --red: #ffe8e8;
    --yellow: #fff0e3;
    --blue-60: rgba(32, 46, 72, 0.6);
    --blue-90: rgba(32, 46, 72, 0.9);
    --orange-10: rgba(247, 110, 17, 0.1);
    --shadow-sm: 0 -1px 4px rgba(0, 0, 0, 0.05);
    --brand-blue-primary: #202e48;
    --brand-gray-selection: rgba(255, 255, 255, 0.1);
    --background: #ffffff;
    --foreground: #171717;

    /* Animation durations */
    --duration-fast: 150ms;
    --duration-normal: 300ms;
    --duration-slow: 500ms;

    /* Timing functions */
    --ease-default: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-bounce: cubic-bezier(0.34, 1.56, 0.64, 1);
    --ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);

    /* Accessibility variables */
    --focus-ring-color: var(--blue-primary);
    --focus-ring-width: 2px;
    --focus-ring-offset: 2px;

    /* Spacing for touch targets */
    --min-touch-target-size: 44px;

    /* Font sizes with good readability */
    --font-size-base: 16px;
    --line-height-base: 1.5;
    --letter-spacing-base: 0.01em;
}

/* High contrast mode */
.high-contrast {
    --blue-primary: #0000ff;
    --orange-primary: #ff6600;
    --grey-01: #cccccc;
    --grey-02: #eeeeee;
    --grey-03: #f5f5f5;
    --grey-04: #fafafa;
    --background: #ffffff;
    --foreground: #000000;
    --focus-ring-color: #ff0000;
    --focus-ring-width: 3px;

    /* Increase contrast for text */
    --blue-60: rgba(0, 0, 255, 0.8);
    --blue-90: rgba(0, 0, 255, 1);

    /* Higher contrast for interactive elements */
    color-scheme: light;
    color: #000000;
    background-color: #ffffff;
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

body {
    font-family: 'Poppins', sans-serif;
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
    letter-spacing: var(--letter-spacing-base);
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Accessibility focus styles */
*:focus-visible {
    outline: var(--focus-ring-width) solid var(--focus-ring-color);
    outline-offset: var(--focus-ring-offset);
}

/* Ensure sufficient touch target size for interactive elements */
button,
a,
input[type='button'],
input[type='submit'],
input[type='reset'],
input[type='checkbox'],
input[type='radio'] {
    min-height: var(--min-touch-target-size);
    min-width: var(--min-touch-target-size);
}

/* Ensure proper spacing between interactive elements */
button:not(:last-child),
a:not(:last-child) {
    margin-right: 8px;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.scrollbar-hide::-webkit-scrollbar {
    display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
}

/* Skip link for keyboard navigation */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}

.sr-only.focus-visible,
.sr-only:focus-visible {
    position: fixed;
    width: auto;
    height: auto;
    padding: 0.75rem;
    margin: 0;
    overflow: visible;
    clip: auto;
    white-space: normal;
    z-index: 9999;
}

/* Shimmer effect animation */
.skeleton-loader .shimmer {
    animation: shimmer 1.6s infinite linear;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        to right,
        rgba(229, 231, 235, 0) 0%,
        rgba(255, 255, 255, 0.6) 50%,
        rgba(229, 231, 235, 0) 100%
    );
    background-size: 200% 100%;
}

/* Common animation keyframes */
@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Common animation classes */
.animate-fade-in {
    animation: fadeIn var(--duration-normal) var(--ease-out) forwards;
}

.animate-fade-in-up {
    animation: fadeInUp var(--duration-normal) var(--ease-out) forwards;
}

.animate-scale-in {
    animation: scaleIn var(--duration-normal) var(--ease-spring) forwards;
}

/* Common transition classes */
.transition-fast {
    transition: all var(--duration-fast) var(--ease-default);
}

.transition-normal {
    transition: all var(--duration-normal) var(--ease-default);
}

.transition-slow {
    transition: all var(--duration-slow) var(--ease-default);
}

/* Common gradient backgrounds */
.bg-gradient-blue {
    background: linear-gradient(to bottom, var(--white), var(--grey-03));
}

.bg-gradient-hero {
    background: linear-gradient(to bottom, var(--white), var(--grey-02));
}

/* :root {
    --background: #ffffff;
    --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
    :root {
        --background: #0a0a0a;
        --foreground: #ededed;
    }
}

body {
    color: var(--foreground);
    background: var(--background);
    font-family: Arial, Helvetica, sans-serif;
} */
