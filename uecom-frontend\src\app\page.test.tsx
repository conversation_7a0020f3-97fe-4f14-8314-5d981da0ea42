import { render } from '@testing-library/react'
import '@testing-library/jest-dom'
import Home from '@/app/page'

describe('Home Component', () => {
    it('renders without crashing', () => {
        const { getByText } = render(<Home />)
        expect(getByText('Welcome to Ecom App')).toBeInTheDocument()
    })

    it('contains the correct heading', () => {
        const { getByRole } = render(<Home />)
        const heading = getByRole('heading', { level: 1 })
        expect(heading).toHaveTextContent('Welcome to Ecom App')
    })

    it('contains the correct paragraph text', () => {
        const { getByText } = render(<Home />)
        expect(
            getByText('A simple yet elegant homepage built with Next.js and Tailwind CSS.'),
        ).toBeInTheDocument()
    })

    it('applies correct background gradient styling', () => {
        const { container } = render(<Home />)
        expect(container.firstChild).toHaveClass('bg-gradient-to-br from-gray-300 to-gray-500')
    })

    it('applies hover effect on heading', () => {
        const { getByRole } = render(<Home />)
        const heading = getByRole('heading', { level: 1 })
        expect(heading).toHaveClass('hover:from-purple-500 hover:to-blue-400')
    })

    it('applies hover effect on paragraph text', () => {
        const { getByText } = render(<Home />)
        const paragraph = getByText(
            'A simple yet elegant homepage built with Next.js and Tailwind CSS.',
        )
        expect(paragraph).toHaveClass('hover:text-white')
    })
})
