'use client'

import {
    Edit2,
    MapPin,
    Settings,
    Info,
    LogOut,
    Phone,
    Mail,
    FileText,
    Headphones,
    ChevronLeft,
    ChevronDown,
} from 'lucide-react'
import { useState, useEffect } from 'react'
import AccountMenu from '@/components/navigation/accoutMenu'
import AppLayout from '@/components/layout/AppLayout'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import LoadingSpinner from '@/components/ui/atoms/LoadingSpinner'

function ProfilePageComponent() {
    const [openMenu, setOpenMenu] = useState(null)
    const { isAuthenticated, isReady, isLoading, requireAuth, logout } = useAuth()

    const router = useRouter()

    // Synchronize authentication state between client and server
    useEffect(() => {
        // Check if we have isVerified in localStorage but not in cookies
        const isVerifiedInLocalStorage = localStorage.getItem('isVerified') === 'true'
        const isVerifiedInCookie = document.cookie.includes('isVerified=true')

        if (isVerifiedInLocalStorage && !isVerifiedInCookie) {
            // Set the cookie for server-side middleware
            document.cookie = 'isVerified=true; path=/; max-age=86400' // 24 hours
            console.log('[Profile] Synchronized auth state: Set isVerified cookie')
        }
    }, [])

    // Check authentication on mount
    useEffect(() => {
        if (isReady && !isLoading) {
            const authResult = requireAuth()
            console.log('[Profile] Auth check result:', authResult)

            if (!authResult) {
                console.log('[Profile] Not authenticated, redirecting to auth page')
            }
        }
    }, [isReady, isLoading, requireAuth])

    const toggleMenu = (menu: any) => {
        setOpenMenu(openMenu === menu ? null : menu)
    }

    // Show loading state while checking authentication
    if (!isReady || isLoading) {
        return <LoadingSpinner />
    }

    // Only render the profile page if the user is authenticated
    // The requireAuth function will handle redirection if not authenticated
    if (!isAuthenticated) {
        return null
    }

    return (
        <AppLayout sections={[]}>
            <main className="flex flex-col items-center pb-[200px] sm:pb-[130px] md:pb-[150px] lg:pb-[180px] xl:pb-[200px]">
                <div className="flex justify-center items-start w-full max-w-[1000px] mx-auto px-4 lg:px-8 py-6 lg:py-12 bg-white">
                    <div className="flex w-full gap-8">
                        <AccountMenu />
                        <div className="flex-1 lg:mt-10">
                            {/* Profile Label with Back Icon - Visible only on Mobile */}
                            <div className="flex items-center gap-3 bg-white lg:hidden">
                                <button
                                    onClick={
                                        typeof window !== 'undefined'
                                            ? () => router.back()
                                            : undefined
                                    }
                                >
                                    <ChevronLeft className="h-5 w-5 text-blue" />
                                </button>
                                <span className="text-[20px] font-poppins font-bold  text-blue-primary">
                                    Profile
                                </span>
                            </div>

                            <div className="max-w-[800px] mx-auto  border border-gray-200 mt-4">
                                <div className="bg-white rounded-lg p-2 lg:m-4">
                                    {/* Contact Info */}
                                    <div className="space-y-4">
                                        {/* Phone Number */}
                                        <div className="relative flex items-center gap-3 font-poppins font-medium">
                                            <Phone className="h-5 w-5 text-blue" />
                                            <span className="text-[15px] text-blue">
                                                9123456780
                                            </span>
                                            <Edit2 className="absolute right-0 h-4 w-4 text-blue/60 cursor-pointer" />
                                        </div>

                                        {/* Email Address */}
                                        <div className="flex items-center gap-3 font-poppins font-medium">
                                            <Mail className="h-5 w-5 text-blue" />
                                            <span className="text-[15px] text-blue">
                                                <EMAIL>
                                            </span>
                                        </div>
                                    </div>

                                    {/* Quick Actions */}
                                    <div className="grid grid-cols-2 gap-2 mt-6 lg:hidden">
                                        {/* Booking History */}
                                        <Link
                                            href="/"
                                            className="flex items-center justify-center gap-1 py-5 bg-[#F5F8FD] rounded-xl text-[15px] text-blue-primary border border-[#E2E8F0] shadow-sm h-[60px]"
                                        >
                                            <FileText size={24} strokeWidth={1.5} />
                                            <span className="font-poppins font-bold">
                                                Booking History
                                            </span>
                                        </Link>

                                        {/* Help & Support */}
                                        <Link
                                            href="/help"
                                            className="flex items-center justify-center gap-1 py-5 bg-[#F5F8FD] rounded-xl text-[15px] text-blue-primary border border-[#E2E8F0] shadow-sm h-[60px]"
                                        >
                                            <Headphones size={24} strokeWidth={1.5} />
                                            <span className="font-poppins font-bold">
                                                Help & Support
                                            </span>
                                        </Link>
                                    </div>

                                    {/* Menu Items */}
                                    <div className="space-y-3 mt-6 font-poppins font-regular">
                                        {/* Address */}
                                        <div className="border-b border-gray-300">
                                            <button
                                                className="flex items-center justify-between w-full h-[52px] rounded-xl text-[15px] text-blue"
                                                onClick={
                                                    typeof window !== 'undefined'
                                                        ? () => toggleMenu('address')
                                                        : undefined
                                                }
                                            >
                                                <div className="flex items-center gap-3">
                                                    <MapPin className="h-5 w-5 text-blue/60" />
                                                    <span>Address</span>
                                                </div>
                                                <span
                                                    className={`transform transition-transform ${
                                                        openMenu === 'address' ? 'rotate-180' : ''
                                                    }`}
                                                >
                                                    <ChevronDown className="h-5 w-5 text-gray-600" />
                                                </span>
                                            </button>
                                            {openMenu === 'address' && (
                                                <div className="p-4 bg-gray-100 rounded-lg">
                                                    Address Details...
                                                </div>
                                            )}
                                        </div>

                                        {/* Settings */}
                                        <div className="border-b border-gray-300">
                                            <button
                                                className="flex items-center justify-between w-full h-[52px] rounded-xl text-[15px] text-blue"
                                                onClick={
                                                    typeof window !== 'undefined'
                                                        ? () => toggleMenu('settings')
                                                        : undefined
                                                }
                                            >
                                                <div className="flex items-center gap-3">
                                                    <Settings className="h-5 w-5 text-blue/60" />
                                                    <span>Settings</span>
                                                </div>
                                                <span
                                                    className={`transform transition-transform ${
                                                        openMenu === 'settings' ? 'rotate-180' : ''
                                                    }`}
                                                >
                                                    <ChevronDown className="h-5 w-5 text-gray-600" />
                                                </span>
                                            </button>
                                            {openMenu === 'settings' && (
                                                <div className="p-4 bg-gray-100 rounded-lg">
                                                    Settings Options...
                                                </div>
                                            )}
                                        </div>

                                        {/* About WIFY */}
                                        <div>
                                            <button
                                                className="flex items-center justify-between w-full h-[52px] rounded-xl text-[15px] text-blue"
                                                onClick={
                                                    typeof window !== 'undefined'
                                                        ? () => toggleMenu('about')
                                                        : undefined
                                                }
                                            >
                                                <div className="flex items-center gap-3">
                                                    <Info className="h-5 w-5 text-blue/60" />
                                                    <span>About WIFY</span>
                                                </div>
                                                <span
                                                    className={`transform transition-transform ${
                                                        openMenu === 'about' ? 'rotate-180' : ''
                                                    }`}
                                                >
                                                    <ChevronDown className="h-5 w-5 text-gray-600" />
                                                </span>
                                            </button>
                                            {openMenu === 'about' && (
                                                <div className="p-4 bg-gray-100 rounded-lg">
                                                    About WIFY Details...
                                                </div>
                                            )}
                                        </div>
                                    </div>

                                    {/* Logout Button */}
                                    <button
                                        className="flex items-center gap-3 mt-6 text-[15px] text-blue sm:block md:hidden"
                                        onClick={
                                            typeof window !== 'undefined'
                                                ? async () => {
                                                      try {
                                                          await logout()
                                                          router.push('/auth')
                                                      } catch (error) {
                                                          console.error('Logout error:', error)
                                                      }
                                                  }
                                                : undefined
                                        }
                                    >
                                        <LogOut className="h-5 w-5" />
                                        <span>Logout</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </AppLayout>
    )
}

// Use dynamic import to ensure client-side only rendering
import dynamic from 'next/dynamic'

const ProfilePage = dynamic(() => Promise.resolve(ProfilePageComponent), {
    ssr: false,
    loading: () => (
        <div className="flex items-center justify-center min-h-screen bg-white">
            <LoadingSpinner />
        </div>
    ),
})

export default ProfilePage
