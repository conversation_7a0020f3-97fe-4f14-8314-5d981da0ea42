'use client'

import { Box, Container, Input, InputGroup, InputLeftElement, Text, VStack } from '@chakra-ui/react'
import { Search as SearchIcon } from 'lucide-react'
import { useEffect, useRef } from 'react'

export default function SearchPage() {
    const inputRef = useRef<HTMLInputElement>(null)

    useEffect(() => {
        inputRef.current?.focus()
    }, [])

    return (
        <Container maxW="container.xl" py={4}>
            <VStack spacing={8} align="stretch">
                <Box>
                    <InputGroup size="lg">
                        <InputLeftElement pointerEvents="none">
                            <SearchIcon color="gray.300" />
                        </InputLeftElement>
                        <Input
                            ref={inputRef}
                            placeholder="Search for products, services..."
                            bg="white"
                            boxShadow="md"
                        />
                    </InputGroup>
                </Box>
                <Box>
                    {/* You can map over and display search results here */}
                    <Text>Search results will appear here.</Text>
                </Box>
            </VStack>
        </Container>
    )
}
