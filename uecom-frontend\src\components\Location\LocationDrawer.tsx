'use client'

import { ArrowLef<PERSON>, Clock, Home, LocateFixed } from 'lucide-react'
import { useLocation } from '../../providers/LocationProvider'

export default function LocationDrawer() {
    const { isLocationOpen, closeLocation } = useLocation()

    return (
        <>
            {/* Overlay */}
            <div
                className={`fixed inset-0 bg-black bg-opacity-30 z-40 transition-opacity duration-300 ${
                    isLocationOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
                }`}
                onClick={closeLocation}
            />

            {/* Drawer */}
            <div
                className={`fixed top-0 right-0 h-full w-full bg-white z-50 transform transition-transform duration-300 ease-in-out ${
                    isLocationOpen ? 'translate-x-0' : 'translate-x-full'
                }`}
            >
                <div className="flex flex-col h-full">
                    <div className="p-4">
                        {/* Search Input */}
                        <div className="relative">
                            <div className="absolute inset-y-0 left-0 flex items-center">
                                <button
                                    aria-label="Go back"
                                    onClick={closeLocation}
                                    className="p-2 text-gray-600 hover:bg-gray-100 rounded-full"
                                >
                                    <ArrowLeft size={24} />
                                </button>
                            </div>
                            <input
                                type="text"
                                placeholder="Search for your location/society/apartment"
                                className="w-full h-12 px-12 text-md bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-blue-500 focus:outline-none"
                            />
                        </div>

                        {/* Use Current Location Button */}
                        <button className="flex items-center space-x-3 w-full text-left py-4 text-purple-600">
                            <LocateFixed size={20} />
                            <span className="font-semibold">Use current location</span>
                        </button>
                    </div>

                    <div className="border-t border-gray-100 flex-grow p-4 space-y-6 bg-gray-50">
                        {/* Saved Locations */}
                        <div>
                            <p className="text-sm font-semibold text-gray-500 mb-2">SAVED</p>
                            <div className="flex items-start space-x-4">
                                <Home size={24} className="text-gray-500 mt-1" />
                                <div>
                                    <p className="font-bold text-gray-800">Home</p>
                                    <p className="text-gray-600 text-sm">
                                        303/B/16 Sangharsh society kukreja compound, Mahul Gaon,
                                        MMRDA Colony, Chembur, Mumbai, Maharashtra 4...
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* Recent Locations */}
                        <div>
                            <p className="text-sm font-semibold text-gray-500 mb-2">RECENTS</p>
                            <div className="flex items-center space-x-4">
                                <Clock size={24} className="text-gray-500" />
                                <div>
                                    <p className="font-bold text-gray-800">Andheri East</p>
                                    <p className="text-gray-600 text-sm">
                                        Mumbai, Maharashtra, India
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}
