interface PhoneInputProps {
    value: string | undefined
    onChange: (value: string | undefined) => void
    onEnter?: () => void
}

export default function PhoneInput({ value, onChange, onEnter }: PhoneInputProps) {
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter' && onEnter) {
            onEnter()
        }
    }

    return (
        <div className="flex items-center w-full gap-[2px]">
            <div className="flex items-center px-2 bg-white border border-[#0000000] rounded-md h-10 gap-[2px]">
                <img
                    src="https://flagcdn.com/w40/in.png"
                    onError={
                        typeof window !== 'undefined'
                            ? (e) => (e.currentTarget.src = '/fallback-flag.png')
                            : undefined
                    }
                    alt="India"
                    width={24.5}
                    height={24.5}
                    className="rounded"
                />
                <span className="font-normal text-black">+91</span>
            </div>
            <div className="flex-grow outline-none">
                <input
                    type="tel"
                    value={value ?? ''}
                    onChange={(e) => {
                        const numericValue = e.target.value.replace(/\D/g, '')
                        onChange(numericValue.slice(0, 10))
                    }}
                    onKeyDown={handleKeyDown}
                    placeholder="Enter phone number"
                    className="w-full p-2 pl-6 outline-none text-black bg-white border border-[#0000000] rounded-md h-10 focus:outline-none focus:border-blue-500"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    maxLength={10}
                />
            </div>
        </div>
    )
}
