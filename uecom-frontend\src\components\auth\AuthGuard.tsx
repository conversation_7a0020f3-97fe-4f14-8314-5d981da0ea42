'use client'

/**
 * Authentication Guard Component
 *
 * This component protects routes that require authentication.
 * It redirects unauthenticated users to the login page.
 *
 * @module components/auth/AuthGuard
 */

import { useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import LoadingSpinner from '@/components/ui/atoms/LoadingSpinner'

interface AuthGuardProps {
    children: React.ReactNode
    fallback?: React.ReactNode
}

/**
 * Authentication Guard Component
 *
 * @param props - Component props
 * @returns JSX element
 */
export default function AuthGuard({ children, fallback }: AuthGuardProps) {
    const { isAuthenticated, isReady, isLoading, requireAuth } = useAuth()

    // Check authentication on mount and redirect if not authenticated
    useEffect(() => {
        if (isReady && !isLoading) {
            requireAuth()
        }
    }, [isReady, isLoading, requireAuth])

    // Show loading state while checking authentication
    if (!isReady || isLoading) {
        return fallback || <LoadingSpinner />
    }

    // If authenticated, render children
    return isAuthenticated ? <>{children}</> : null
}
