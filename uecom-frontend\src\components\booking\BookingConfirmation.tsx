'use client'

import { Check, Calendar, MapPin } from 'lucide-react'
import { Button } from '@/components/ui/atoms/Button'
import { Card, CardContent } from '@/components/ui/atoms/Card'

interface ServiceBooking {
    name: string
    price: number
    date: string
    time: string
    location: string
}

const bookings: ServiceBooking[] = [
    {
        name: 'Ceiling Fan Installation',
        price: 150,
        date: '23-Oct-2024',
        time: '9pm-10pm',
        location: '201, Apurva Avenue, Hiranandani Gardens, Powai, Mumbai, 400076',
    },
    {
        name: 'Exhaust Fan Installation',
        price: 150,
        date: '23-Oct-2024',
        time: '1pm-2pm',
        location: '201, Apurva Avenue, Hiranandani Gardens, Powai, Mumbai, 400076',
    },
]

export default function BookingConfirmation() {
    const formatPrice = (price: number) => `₹${price}`

    const handleContinue = () => {
        console.log('Continue button clicked')
        // Add your navigation logic here
    }

    return (
        <div className="max-w-md mx-auto bg-white min-h-screen">
            <div className="px-6 py-8">
                {/* Success Icon */}
                <div className="flex justify-center mb-6">
                    <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center">
                        <Check className="w-8 h-8 text-white" strokeWidth={3} />
                    </div>
                </div>

                {/* Title */}
                <h1 className="text-xl font-bold text-blue-primary text-center">
                    Thank You For Booking
                </h1>

                {/* Subtitle */}
                <p className="text-xs font-medium text-gray-600 text-center p-5">
                    Your service has been booked successfully!
                </p>

                {/* Service Bookings */}
                <div className="space-y-4 mb-8">
                    {bookings.map((booking, index) => (
                        <Card key={index} className="border border-gray-300 shadow-sm">
                            <CardContent className="p-4">
                                <div className="flex justify-between items-start mb-3">
                                    <h3 className="font-medium text-gray-800 text-sm">
                                        {booking.name}
                                    </h3>
                                    <span className="font-semibold text-gray-800 text-sm">
                                        {formatPrice(booking.price)}
                                    </span>
                                </div>

                                <div className="space-y-2">
                                    <div className="flex items-center text-xs text-gray-600">
                                        <Calendar className="w-3 h-3 mr-2 text-gray-400" />
                                        {booking.date} At {booking.time}
                                    </div>

                                    <div className="flex items-start text-xs text-gray-600">
                                        <MapPin className="w-3 h-3 mr-2 text-gray-400 mt-0.5 flex-shrink-0" />
                                        <span className="leading-tight">{booking.location}</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {/* Continue Button */}
                <Button
                    onClick={handleContinue}
                    className="w-full bg-blue-primary hover:bg-slate-800 text-white py-3 rounded-lg font-medium"
                >
                    Continue
                </Button>
            </div>
        </div>
    )
}
