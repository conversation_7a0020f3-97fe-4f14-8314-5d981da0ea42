'use client'

import MobileBottomNavigation from '@/components/navigation/mobileBottomNavigation'
import Footer from '@/components/sections/Footer'
import Header from '@/components/sections/Header'
import { FooterSection, HeaderSection, SectionData } from '@/types'
import React from 'react'
import { useDeviceContext } from '../../providers/DeviceProvider'
import { useLocation } from '../../providers/LocationProvider'
import { useSearch } from '../../providers/SearchProvider'
import LocationDrawer from '../Location/LocationDrawer'
import SearchDrawer from '../search/SearchDrawer'
import { SectionRenderer } from './SectionRenderer'

interface AppLayoutProps {
    /**
     * Content to render in the main area
     */
    children?: React.ReactNode

    /**
     * Optional sections data for dynamic rendering
     * If provided, children will be wrapped in these sections
     */
    sections?: SectionData[]

    /**
     * Whether to show the mobile navigation
     * @default true
     */
    showMobileNav?: boolean
}

/**
 * Unified layout component that can be used in two ways:
 * 1. With sections data for dynamic rendering (used by HomePage)
 * 2. With children for static content (used by other pages)
 *
 * This component consolidates the functionality of both PageLayout and bodyLayout
 */
export default function AppLayout({
    children,
    sections = [],
    showMobileNav = true,
}: AppLayoutProps) {
    const { isSearchOpen } = useSearch()
    const { isLocationOpen } = useLocation()
    const { isMobile } = useDeviceContext()

    // Find header and footer sections if sections are provided and they are visible
    const headerSection = sections.find(
        (section) => section.type === 'header' && section.visible !== false,
    ) as HeaderSection | undefined

    const footerSection = sections.find(
        (section) => section.type === 'footer' && section.visible !== false,
    ) as FooterSection | undefined

    // Get all other sections (excluding header and footer) and filter by visibility
    const contentSections = sections.filter(
        (section) =>
            section.type !== 'header' && section.type !== 'footer' && section.visible !== false, // Only show sections that are visible (undefined or true)
    )

    // Determine if we're using dynamic sections or static children
    const hasDynamicSections = contentSections.length > 0

    // Calculate the slide transform for mobile only
    const slideTransform =
        isMobile && (isSearchOpen || isLocationOpen) ? 'translateX(-100%)' : 'translateX(0)'

    return (
        <div>
            <div
                className="flex flex-col min-h-screen bg-white transition-transform duration-300 ease-in-out"
                style={{
                    transform: slideTransform,
                    willChange: 'transform',
                }}
            >
                {/* Header - either from sections or direct component */}
                <div>
                    {headerSection ? (
                        <SectionRenderer section={headerSection} index={0} />
                    ) : (
                        <Header data={null} />
                    )}
                </div>

                {/* Main content area */}
                <main className="flex-grow">
                    {hasDynamicSections
                        ? // Render dynamic sections
                          contentSections.map((section, index) => (
                              <SectionRenderer
                                  key={`${section.type}-${index}`}
                                  section={section}
                                  index={index}
                              />
                          ))
                        : // Render static children
                          children}
                </main>

                {/* Footer - either from sections or direct component */}
                <div>
                    {footerSection ? (
                        <SectionRenderer section={footerSection} index={sections.length - 1} />
                    ) : (
                        <Footer data={null} />
                    )}
                </div>

                {/* Mobile navigation - fixed to bottom of screen */}
                {showMobileNav && (
                    <div className="lg:hidden">
                        <MobileBottomNavigation />
                    </div>
                )}
            </div>

            {/* Global Drawers */}
            <SearchDrawer />
            <LocationDrawer />
        </div>
    )
}
