import { Box, Flex, Text, IconButton } from '@chakra-ui/react'
import { Bell, ShoppingCart } from 'lucide-react'
import React from 'react'

export default function Header() {
    return (
        <Flex
            as="header"
            align="center"
            justify="space-between"
            py={4}
            px={{ base: 4, md: 6 }}
            bg="white"
            borderBottom="1px"
            borderColor="gray.100"
            position="sticky"
            top={0}
            zIndex={100}
        >
            <Box>
                <Text fontSize="2xl" fontWeight="bold" color="blue.600">
                    WIFY
                </Text>
                <Text fontSize="sm" color="blue.500" mt={1}>
                    Book Trained Technicians for home Services
                </Text>
            </Box>
            <Flex align="center">
                <IconButton
                    aria-label="Notifications"
                    icon={<Bell size={20} />}
                    variant="ghost"
                    colorScheme="gray"
                    mr={2}
                />
                <IconButton
                    aria-label="Shopping Cart"
                    icon={<ShoppingCart size={20} />}
                    variant="ghost"
                    colorScheme="gray"
                />
                {/* Placeholder for cart item count */}
                <Box
                    position="absolute"
                    top={4}
                    right={4}
                    bg="orange.400"
                    color="white"
                    borderRadius="full"
                    fontSize="xs"
                    px="1.5"
                    py="0.5"
                    lineHeight="none"
                >
                    2
                </Box>
            </Flex>
        </Flex>
    )
}
