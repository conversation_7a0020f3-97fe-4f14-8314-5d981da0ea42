'use client'

import React, { useMemo, Suspense, useState, useEffect } from 'react'
import { SectionData } from '../../types'
import { sectionFactory } from '../../factories/SectionFactory'
import SkeletonLoader from '../ui/atoms/SkeletonLoader'
import ErrorFallback from '../ui/molecules/ErrorFallback'
import ErrorBoundary from '../ui/molecules/ErrorBoundary'
import { ClientSectionRenderer } from './ClientSectionRenderer'
import { usePathname } from 'next/navigation'
import { useStaticText } from '@/hooks/useStaticText'

interface SectionRendererProps {
    section: SectionData
    index: number
    /**
     * Optional fallback component to show while loading
     */
    customFallback?: React.ReactNode
}

/**
 * SectionRenderer component
 * Server component that renders a section based on its type using the SectionFactory
 * Delegates client-side loading animations to ClientSectionRenderer
 *
 * @param section - Section data to render
 * @param index - Index of the section for key generation
 */
export function SectionRenderer({ section, index, customFallback }: SectionRendererProps) {
    // Get static text
    const { t } = useStaticText()

    // Get the current pathname to determine if we should show loading effects
    const pathname = usePathname()

    // Track error state
    const [hasError, setHasError] = useState(false)
    const [errorMessage, setErrorMessage] = useState('')

    // Determine if we should disable loading effects for this section
    const disableLoading = useMemo(() => {
        // Disable loading effects on all routes except discovery
        if (!pathname.includes('/discovery')) {
            return true
        }

        // For discovery route, disable loading for specific sections that should render immediately
        // Especially brand-list to prevent image flickering
        // const sectionsWithoutLoading = ['header', 'footer', 'navigation', 'brand-list']
        const sectionsWithoutLoading = ['navigation']
        return sectionsWithoutLoading.includes(section.type)
    }, [pathname, section.type])

    // Memoize the component check and retrieval to improve performance
    const SectionComponent = useMemo(() => {
        try {
            // Check if we have a component for this section type
            if (!sectionFactory.hasComponent(section.type)) {
                console.warn(`No component found for section type: ${section.type}`)
                setHasError(true)
                setErrorMessage(`No component found for section type: ${section.type}`)
                return null
            }

            // Get the component for this section type
            return sectionFactory.getComponent(section.type)
        } catch (error) {
            console.error(`Error getting component for section type ${section.type}:`, error)
            setHasError(true)
            setErrorMessage(`Failed to load component for section type: ${section.type}`)
            return null
        }
    }, [section.type])

    // Reset error state when section type changes
    useEffect(() => {
        setHasError(false)
        setErrorMessage('')
    }, [section.type])

    // If no component is found or there was an error, show an error message
    if (!SectionComponent || hasError) {
        // Create hardcoded fallback messages in case translations fail
        const fallbackTitle = 'Unknown Section Type'
        const fallbackMessage = `No component found for section type: ${section.type}`
        const fallbackRetry = 'Try again'

        // Try to get translations, but use fallbacks if they fail
        let titleText = fallbackTitle
        let messageText = errorMessage || fallbackMessage
        let retryText = fallbackRetry

        try {
            // Only try to use translations if t is available
            if (typeof t === 'function') {
                titleText = t('errors.unknown_section') || fallbackTitle

                if (!errorMessage) {
                    messageText =
                        t('errors.no_component_found', { type: section.type }) || fallbackMessage
                }

                retryText = t('errors.try_again') || fallbackRetry
            }
        } catch (error) {
            console.error('Translation error in error message:', error)
            // Use fallbacks if translation fails
        }

        return (
            <div
                className="p-4 bg-red-50 border border-red-200 rounded-md"
                role="alert"
                aria-live="assertive"
            >
                <h3 className="text-red-800 font-medium">{titleText}</h3>
                <p className="text-red-600">{messageText}</p>
                <button
                    onClick={
                        typeof window !== 'undefined'
                            ? () => {
                                  setHasError(false)
                                  setErrorMessage('')
                              }
                            : undefined
                    }
                    className="mt-3 px-4 py-2 bg-red-100 hover:bg-red-200 text-red-800 rounded-md transition-colors"
                >
                    {retryText}
                </button>
            </div>
        )
    }

    // Generate skeleton loader based on section type
    const getSectionSkeleton = () => {
        switch (section.type) {
            case 'header':
                return (
                    <div className="p-4 space-y-2">
                        <div className="flex justify-between items-center">
                            <SkeletonLoader
                                width="w-[120px]"
                                height="h-[40px]"
                                variant="rect"
                                rounded="rounded-md"
                            />
                            <div className="flex space-x-4">
                                <SkeletonLoader
                                    variant="rect"
                                    width="w-[80px]"
                                    height="h-[32px]"
                                    rounded="rounded-md"
                                />
                                <SkeletonLoader
                                    variant="rect"
                                    width="w-[80px]"
                                    height="h-[32px]"
                                    rounded="rounded-md"
                                />
                                <SkeletonLoader
                                    variant="rect"
                                    width="w-[80px]"
                                    height="h-[32px]"
                                    rounded="rounded-md"
                                />
                            </div>
                        </div>
                    </div>
                )

            case 'footer':
                return (
                    <div className="p-4 space-y-4">
                        <div className="grid grid-cols-3 md:grid-cols-4 gap-4">
                            <SkeletonLoader height="h-[120px]" rounded="rounded-md" />
                            <SkeletonLoader height="h-[120px]" rounded="rounded-md" />
                            <SkeletonLoader height="h-[120px]" rounded="rounded-md" />
                            <SkeletonLoader
                                height="h-[120px]"
                                rounded="rounded-md"
                                className="hidden md:block"
                            />
                        </div>
                        <SkeletonLoader height="h-[40px]" width="w-full" rounded="rounded-md" />
                    </div>
                )

            case 'search-service':
                return (
                    <div className="p-4 space-y-4">
                        {/* Title and Subheading (Desktop) */}
                        <div className="hidden md:block text-center">
                            <SkeletonLoader
                                width="w-[60%]"
                                height="h-[40px]"
                                rounded="rounded-md"
                                className="mx-auto mb-2"
                            />
                            <SkeletonLoader
                                width="w-[40%]"
                                height="h-[24px]"
                                rounded="rounded-md"
                                className="mx-auto mb-4"
                            />
                        </div>
                        {/* Search Bar */}
                        <SkeletonLoader
                            width="w-[90%]"
                            height="h-[60px]"
                            rounded="rounded-md"
                            className="mx-auto"
                        />
                    </div>
                )

            case 'service-categories':
                return (
                    <div className="p-4">
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                            {[...Array(6)].map((_, i) => (
                                <SkeletonLoader key={i} height="h-[80px]" rounded="rounded-md" />
                            ))}
                        </div>
                    </div>
                )

            case 'brand-list':
                return (
                    <div className="p-4">
                        <div className="grid grid-cols-3 md:grid-cols-5 gap-4">
                            {[...Array(10)].map((_, i) => (
                                <SkeletonLoader
                                    key={i}
                                    width="w-full"
                                    height="h-[50px]"
                                    rounded="rounded-xl"
                                />
                            ))}
                        </div>
                    </div>
                )

            case 'banner':
                return (
                    <div className="p-4">
                        <SkeletonLoader width="w-full" height="h-[200px]" rounded="rounded-lg" />
                    </div>
                )

            case 'review-list':
                return (
                    <div className="p-4 space-y-4">
                        {[...Array(3)].map((_, i) => (
                            <div key={i} className="p-4 border border-gray-200 rounded-lg">
                                <SkeletonLoader
                                    width="w-[80%]"
                                    height="h-[20px]"
                                    rounded="rounded-md"
                                    className="mb-2"
                                />
                                <SkeletonLoader
                                    width="w-full"
                                    height="h-[60px]"
                                    rounded="rounded-md"
                                    className="mb-2"
                                />
                                <div className="flex items-center">
                                    <SkeletonLoader
                                        variant="circle"
                                        width="w-[40px]"
                                        height="h-[40px]"
                                        className="mr-2"
                                    />
                                    <SkeletonLoader
                                        width="w-[120px]"
                                        height="h-[20px]"
                                        rounded="rounded-md"
                                    />
                                </div>
                            </div>
                        ))}
                    </div>
                )

            case 'stats':
                return (
                    <div className="p-4">
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            {[...Array(4)].map((_, i) => (
                                <div key={i} className="text-center">
                                    <SkeletonLoader
                                        variant="circle"
                                        width="w-[60px]"
                                        height="h-[60px]"
                                        className="mx-auto mb-2"
                                    />
                                    <SkeletonLoader
                                        width="w-[80px]"
                                        height="h-[24px]"
                                        rounded="rounded-md"
                                        className="mx-auto mb-1"
                                    />
                                    <SkeletonLoader
                                        width="w-[100px]"
                                        height="h-[16px]"
                                        rounded="rounded-md"
                                        className="mx-auto"
                                    />
                                </div>
                            ))}
                        </div>
                    </div>
                )

            case 'b2b-registration':
                return (
                    <div className="p-4 space-y-4">
                        <SkeletonLoader
                            width="w-[60%]"
                            height="h-[30px]"
                            rounded="rounded-md"
                            className="mb-4"
                        />
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                            {[...Array(3)].map((_, i) => (
                                <div key={i} className="flex items-center">
                                    <SkeletonLoader
                                        variant="circle"
                                        width="w-[30px]"
                                        height="h-[30px]"
                                        className="mr-2"
                                    />
                                    <SkeletonLoader
                                        width="w-[120px]"
                                        height="h-[20px]"
                                        rounded="rounded-md"
                                    />
                                </div>
                            ))}
                        </div>
                        <SkeletonLoader width="w-[150px]" height="h-[40px]" rounded="rounded-md" />
                    </div>
                )

            // Default skeleton for any other section type
            default:
                return (
                    <div className="p-4">
                        <SkeletonLoader width="w-full" height="h-[150px]" rounded="rounded-md" />
                    </div>
                )
        }
    }

    // Loading fallback using skeleton loaders or custom fallback if provided
    const loadingFallback = useMemo(() => {
        return customFallback || getSectionSkeleton()
    }, [customFallback, section.type])

    // Error fallback using our reusable component
    const errorFallback = useMemo(() => {
        // Create hardcoded fallback messages in case translations fail
        const fallbackTitle = 'Error loading section'
        const fallbackMessage = `We're sorry, but there was an error loading the ${section.label || section.type} section.`

        // Try to get translations, but use fallbacks if they fail
        let titleText = fallbackTitle
        let messageText = fallbackMessage

        try {
            // Only try to use translations if t is available
            if (typeof t === 'function') {
                titleText = t('errors.section_error_title') || fallbackTitle
                messageText =
                    t('errors.section_error_message', { section: section.label || section.type }) ||
                    fallbackMessage
            }
        } catch (error) {
            console.error('Translation error:', error)
            // Use fallbacks if translation fails
        }

        return (
            <ErrorFallback
                title={titleText}
                message={messageText}
                onRetry={() => {
                    // Reset error state to trigger a re-render
                    setHasError(false)
                    setErrorMessage('')
                }}
            />
        )
    }, [section.label, section.type])

    // Render with ClientSectionRenderer for client-side loading effects
    return (
        <Suspense fallback={loadingFallback}>
            <ClientSectionRenderer
                section={section}
                index={index}
                loadingFallback={loadingFallback}
                errorFallback={errorFallback}
                disableLoading={disableLoading}
            >
                {/* Wrap in error boundary to catch rendering errors */}
                <ErrorBoundary fallback={errorFallback}>
                    <SectionComponent data={section} />
                </ErrorBoundary>
            </ClientSectionRenderer>
        </Suspense>
    )
}
