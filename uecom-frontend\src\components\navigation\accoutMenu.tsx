'use client'

import { User, Calendar, Headphones, LogOut, ChevronLeft } from 'lucide-react'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import clsx from 'clsx'

export default function AccountMenu() {
    const pathname = usePathname()

    const menuItems = [
        { name: 'Profile', icon: <User className="h-5 w-5" />, path: '/profile' },
        { name: 'Booking History', icon: <Calendar className="h-5 w-5" />, path: '/' },
        { name: 'Help & Support', icon: <Headphones className="h-5 w-5" />, path: '/help' },
        { name: 'Logout', icon: <LogOut className="h-5 w-5" />, path: '/' },
    ]

    return (
        <div className="hidden lg:block w-[280px] bg-grey-4 rounded-lg font-poppins font-medium">
            <div className="flex items-center gap-2 px-6 py-4">
                <Link href="/">
                    <ChevronLeft className="text-xs text-blue" />
                </Link>
                <span className="text-[15px] text-blue font-bold">Account</span>
            </div>
            <div className="px-4">
                {menuItems.map((item) => (
                    <Link key={item.name} href={item.path}>
                        <div
                            className={clsx(
                                'flex items-center gap-2 px-4 py-3 rounded-lg cursor-pointer border-l-2',
                                pathname === item.path
                                    ? 'text-orange-primary border-orange-primary bg-white'
                                    : 'text-blue-primary border-transparent ',
                            )}
                        >
                            {item.icon}
                            <span className="text-[15px]">{item.name}</span>
                        </div>
                    </Link>
                ))}
            </div>
        </div>
    )
}
