'use client'

import {
    MapPin,
    Settings,
    Info,
    LogOut,
    Phone,
    Mail,
    FileText,
    Headphones,
    ChevronLeft,
    ChevronDown,
    PenLine,
    ChevronRight,
} from 'lucide-react'
import { useState } from 'react'
import Link from 'next/link'
import AccountMenu from '@/components/navigation/accoutMenu'

const ProfilePage = () => {
    const [openMenu, setOpenMenu] = useState<string | null>(null)

    const toggleMenu = (menu: string) => {
        setOpenMenu(openMenu === menu ? null : menu)
    }

    return (
        <main className="flex flex-col items-center px-4 py-4 sm:pb-[130px] md:pb-[150px] lg:pb-[180px] xl:pb-[200px] bg-white">
            <div className="w-full max-w-[480px] mx-auto">
                {/* Back and Profile Heading */}
                <div className="flex items-center gap-2 mb-6">
                    <ChevronLeft className="h-5 w-5 text-black" />
                    <h1 className="text-lg font-semibold text-black font-poppins">Profile</h1>
                </div>

                {/* Contact Info */}
                <div className="bg-white space-y-4 mb-6 pt-2">
                    <div className="relative flex items-center gap-2">
                        <Phone className="h-4 w-4 text-gray-500" />
                        <span className="text-black font-medium text-xs">9123456780</span>
                        <PenLine className="absolute right-0 h-4 w-4 text-blue cursor-pointer" />
                    </div>
                    <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-gray-500" />
                        <span className="text-black font-medium text-xs">
                            <EMAIL>
                        </span>
                    </div>
                </div>

                {/* Buttons */}
                <div className="grid grid-cols-2 gap-3 mb-6">
                    <Link
                        href="/"
                        className="flex items-center justify-center gap-2 px-2 py-6 bg-[#F5F8FD] rounded-lg border border-[#E2E8F0] shadow-sm"
                    >
                        <FileText size={18} strokeWidth={1.5} className="font-semibold text-blue-primary" />
                        <span className="text-blue-primary font-semibold text-xs">
                            Booking History
                        </span>
                    </Link>
                    <Link
                        href="/help"
                        className="flex items-center justify-center gap-2 px-4 py-6 bg-[#F5F8FD] rounded-lg border border-[#E2E8F0] shadow-sm"
                    >
                        <Headphones size={18} strokeWidth={1.5} className="text-blue-primary" />
                        <span className="text-blue-primary font-semibold text-xs">
                            Help & Support
                        </span>
                    </Link>
                </div>

                {/* Menu Items */}
                <div className="bg-[#F9FAFB] rounded-xl divide-y divide-gray-200 font-poppins">
                    {/* Address */}
                    <button
                        className="w-full flex items-center justify-between py-4 text-sm"
                        onClick={() => toggleMenu('address')}
                    >
                        <div className="flex items-center gap-2 text-[12px] font-normal text-blue-primary">
                            <MapPin className="h-5 w-5" />
                            <span>Address</span>
                        </div>

                        <ChevronRight
                            className={`h-5 w-5 text-blue-primary transition-transform ${
                                openMenu === 'address' ? 'rotate-90' : ''
                            }`}
                        />
                    </button>
                    {openMenu === 'address' && (
                        <div className="px-4 py-2 bg-gray-100 text-sm">Address Details...</div>
                    )}

                    {/* Settings */}
                    <button
                        className="w-full flex items-center justify-between py-4 text-sm text-[#1E2B4F]"
                        onClick={() => toggleMenu('settings')}
                    >
                        <div className="flex items-center gap-2 text-[12px] font-normal text-blue-primary">
                            <Settings className="h-5 w-5" />
                            <span>Settings</span>
                        </div>
                        <ChevronRight
                            className={`h-5 w-5 text-[#1E2B4F] transition-transform ${
                                openMenu === 'settings' ? 'rotate-90' : ''
                            }`}
                        />
                    </button>
                    {openMenu === 'settings' && (
                        <div className="px-4 py-2 bg-gray-100 text-sm">Settings Options...</div>
                    )}

                    {/* About */}
                    <button
                        className="w-full flex items-center justify-between py-4 text-sm text-[#1E2B4F]"
                        onClick={() => toggleMenu('about')}
                    >
                        <div className="flex items-center gap-2 text-[12px] font-normal text-blue-primary">
                            <Info className="h-5 w-5 " />
                            <span>About WIFY</span>
                        </div>
                        <ChevronRight
                            className={`h-5 w-5 text-[#1E2B4F] transition-transform ${
                                openMenu === 'about' ? 'rotate-90' : ''
                            }`}
                        />
                    </button>
                    {openMenu === 'about' && (
                        <div className="px-4 py-2 bg-gray-100 text-sm">About WIFY Details...</div>
                    )}
                </div>

                {/* Logout */}
                <button className="flex items-center gap-2 py-4 text-[12px] font-normal text-blue-primary">
                    <LogOut className="h-5 w-5" />
                    <span>Logout</span>
                </button>
            </div>
        </main>
    )
}

export default ProfilePage
