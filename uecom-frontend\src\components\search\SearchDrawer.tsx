'use client'

import { ArrowLeft, TrendingUp } from 'lucide-react'
import React, { useEffect, useRef, useState } from 'react'
import { useSearch } from '../../providers/SearchProvider'

const trendingSearches = [
    { name: 'Salon', icon: TrendingUp },
    { name: 'Professional bathroom cleaning', icon: TrendingUp },
    { name: 'Washing machine repair', icon: TrendingUp },
    { name: 'Massage for men', icon: TrendingUp },
    { name: 'Professional kitchen cleaning', icon: TrendingUp },
    { name: 'Full home cleaning', icon: TrendingUp },
    { name: 'Spa luxe', icon: TrendingUp },
    { name: 'Carpenter<PERSON>', icon: TrendingUp },
    { name: 'Electricians', icon: TrendingUp },
    { name: 'Ro repair', icon: TrendingUp },
]

export default function SearchDrawer() {
    const { isSearchOpen, closeSearch } = useSearch()
    const inputRef = useRef<HTMLInputElement>(null)
    const [searchQuery, setSearchQuery] = useState('')

    useEffect(() => {
        if (isSearchOpen) {
            setTimeout(() => {
                inputRef.current?.focus()
            }, 100)
        } else {
            setSearchQuery('')
        }
    }, [isSearchOpen])

    const handleTrendingSearch = (searchTerm: string) => {
        setSearchQuery(searchTerm)
    }

    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(event.target.value)
    }

    return (
        <>
            {/* Overlay */}
            <div
                className={`fixed inset-0 bg-black bg-opacity-30 z-40 transition-opacity duration-300 ${
                    isSearchOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
                }`}
                onClick={closeSearch}
            />

            {/* Drawer */}
            <div
                className={`fixed top-0 right-0 h-full w-full max-w-md bg-white z-50 transform transition-transform duration-300 ease-in-out ${
                    isSearchOpen ? 'translate-x-0' : 'translate-x-full'
                }`}
            >
                <div className="p-4 h-full">
                    <div className="flex flex-col space-y-6 h-full">
                        {/* Search Input */}
                        <div className="relative">
                            <div className="absolute inset-y-0 left-0 flex items-center">
                                <button
                                    aria-label="Go back"
                                    onClick={closeSearch}
                                    className="p-2 text-gray-600 hover:bg-gray-100 rounded-full"
                                >
                                    <ArrowLeft size={24} />
                                </button>
                            </div>
                            <input
                                ref={inputRef}
                                type="text"
                                placeholder="Look for services"
                                className="w-full h-12 px-12 text-md bg-white border border-gray-200 rounded-lg focus:ring-1 focus:ring-blue-500 focus:outline-none"
                                value={searchQuery}
                                onChange={handleInputChange}
                            />
                        </div>

                        {/* Trending Searches Section */}
                        <div className="flex-1 overflow-y-auto">
                            <p className="text-lg font-bold mb-4 text-gray-800">
                                Trending searches
                            </p>
                            <div className="flex flex-wrap gap-3">
                                {trendingSearches.map((search) => (
                                    <button
                                        key={search.name}
                                        onClick={() => handleTrendingSearch(search.name)}
                                        className="flex items-center space-x-2 h-auto whitespace-nowrap px-4 py-2 text-md font-normal text-gray-800 bg-white border border-gray-300 rounded-lg hover:bg-gray-100"
                                    >
                                        <TrendingUp size={18} className="text-gray-600" />
                                        <span>{search.name}</span>
                                    </button>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}
