import React, { useState } from 'react'
import { BrandListSection } from '../../types'
import { ArrowRight } from 'lucide-react'
import { useDeviceContext } from '../../providers/DeviceProvider'
import BrandsModal from '../ui/molecules/BrandsModal'

interface BrandListProps {
    data: BrandListSection
}

const BrandList: React.FC<BrandListProps> = ({ data }) => {
    const { label, value } = data
    const { brands } = value
    const { isMobile, isTablet, isMounted } = useDeviceContext()
    const [isModalOpen, setIsModalOpen] = useState(false)

    if (!brands || brands.length === 0) {
        return null
    }

    const handleBrandClick = (brand: { label: string; logoImage: string }) => {
        // Handle brand click action
        console.log(`Brand clicked: ${brand.label}`)
    }

    const handleViewAllClick = () => {
        setIsModalOpen(true)
    }

    const handleModalClose = () => {
        setIsModalOpen(false)
    }

    const MobileView = () => (
        <section className="py-6 mt-6 bg-white block sm:block md:hidden">
            <div className="container mx-auto px-4">
                <h2 className="text-sm font-semibold text-blue-primary mb-4">{label}</h2>

                {/* Grid with 9 brands + View All button as 10th item */}
                <div className="grid grid-cols-4 gap-2" role="list">
                    {brands.slice(0, 7).map((brand, index) => (
                        <button
                            key={`${brand.label}-${index}`}
                            onClick={() => handleBrandClick(brand)}
                            className="bg-white rounded-md px-1 flex items-center justify-center shadow-sm border border-gray-400 w-16 h-16 hover:shadow-md transition-shadow"
                            aria-label={`View services by ${brand.label}`}
                        >
                            <img
                                src={brand.logoImage}
                                alt={`${brand.label} logo`}
                                className="max-h-10 max-w-full object-contain"
                            />
                        </button>
                    ))}

                    {/* View All Button as last grid item */}
                    <button
                        onClick={handleViewAllClick}
                        className="bg-white rounded-md px-1 flex items-center justify-center shadow-sm border border-gray-300 w-16 h-16 hover:shadow-md transition-shadow"
                        aria-label="View all brands"
                    >
                        <span className="text-blue-primary text-xs font-semibold text-center">
                            View All
                        </span>
                    </button>
                </div>
            </div>
        </section>
    )

    const TabletView = () => (
        <section className="py-7 mt-8 bg-white hidden md:block lg:hidden">
            <div className="container mx-auto px-6">
                <h2 className="text-lg font-bold text-[#1E293B] mb-5">{label}</h2>

                {/* Grid with 9 brands + View All button as 10th item */}
                <div className="grid grid-cols-5 gap-3" role="list">
                    {brands.slice(0, 9).map((brand, index) => (
                        <button
                            key={`${brand.label}-${index}`}
                            onClick={() => handleBrandClick(brand)}
                            className="bg-[#F8F9FA] rounded-lg p-3 flex items-center justify-center shadow-sm border border-gray-300 h-20 hover:shadow-md transition-shadow"
                            aria-label={`View services by ${brand.label}`}
                        >
                            <img
                                src={brand.logoImage}
                                alt={`${brand.label} logo`}
                                className="max-h-12 max-w-full object-contain"
                            />
                        </button>
                    ))}

                    {/* View All Button as last grid item */}
                    <button
                        onClick={handleViewAllClick}
                        className="bg-white rounded-lg p-3 flex items-center justify-center shadow-sm border border-gray-300 h-20 hover:shadow-md transition-shadow"
                        aria-label="View all brands"
                    >
                        <span className="text-blue-600 text-sm font-semibold text-center">
                            View All
                        </span>
                    </button>
                </div>
            </div>
        </section>
    )

    const DesktopView = () => (
        <section className="py-8 px-10 bg-white hidden lg:block">
            <div className="container mx-auto">
                <h2 className="text-xl font-bold text-blue-primary mb-6">{label}</h2>

                {/* Horizontal grid with 9 brands + View All button as 10th item */}
                <div className="grid grid-cols-10 gap-4" role="list">
                    {brands.slice(0, 9).map((brand, index) => (
                        <button
                            key={`${brand.label}-${index}`}
                            onClick={() => handleBrandClick(brand)}
                            className="p-4 bg-white rounded-lg shadow-md hover:shadow-xl transition-all duration-200 flex items-center justify-center h-24 border border-gray-100 hover:border-gray-200"
                            aria-label={`View services by ${brand.label}`}
                        >
                            <img
                                src={brand.logoImage}
                                alt={`${brand.label} logo`}
                                className="max-h-12 max-w-full object-contain"
                            />
                        </button>
                    ))}

                    {/* View All Button as last grid item */}
                    <button
                        onClick={handleViewAllClick}
                        className="p-4 bg-white rounded-lg shadow-md hover:shadow-xl transition-all duration-200 flex items-center justify-center h-24 border border-gray-100 hover:border-gray-200"
                        aria-label="View all brands"
                    >
                        <span className="text-blue-600 text-sm font-semibold text-center">
                            View All
                        </span>
                    </button>
                </div>
            </div>
        </section>
    )

    if (!isMounted) return null
    if (!brands || brands.length === 0) return null

    return (
        <>
            {isMobile && <MobileView />}
            {isTablet && <TabletView />}
            {!isMobile && !isTablet && <DesktopView />}

            {/* Brands Modal */}
            <BrandsModal
                isOpen={isModalOpen}
                onClose={handleModalClose}
                brands={brands}
                onBrandClick={handleBrandClick}
            />
        </>
    )
}

export default BrandList
