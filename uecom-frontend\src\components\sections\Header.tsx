'use client'

import { useStaticText } from '@/hooks/useStaticText'
import {
    Box,
    Button,
    Container,
    Input,
    InputGroup,
    InputLeftElement,
    Spinner,
} from '@chakra-ui/react'
import { MapPin, Search } from 'lucide-react'
import Link from 'next/link'
import React, { memo, useEffect, useMemo, useState } from 'react'
import { IoIosArrowDown } from 'react-icons/io'
import { IoCartOutline, IoLocationOutline } from 'react-icons/io5'
import { LuBell } from 'react-icons/lu'
import { useDeviceContext } from '../../providers/DeviceProvider'
import { useLocation } from '../../providers/LocationProvider'
import { useSearch } from '../../providers/SearchProvider'
import { HeaderSection } from '../../types'
import { useLocationLoader } from '@/providers/LocationLoaderProvider'
interface HeaderProps {
    data: HeaderSection | null
}

const MobileView = memo(
    ({
        onSearchOpen,
        onLocationOpen,
        animatedWord,
        locality,
        streetAddress,
        pincode,
        t,
        error,
        loading,
        refetch,
    }: {
        onSearchOpen: () => void
        onLocationOpen: () => void
        animatedWord: string
        locality?: string
        streetAddress?: string
        pincode?: string
        t: (key: string) => string
        error?: string | null
        loading: boolean
        refetch: () => void
    }) => (
        <header
            className="pt-2 block sm:block md:hidden pb-4"
            style={{
                background: 'linear-gradient(to top, #f0f9ff 0%, #ffffff 100%)',
            }}
        >
            <Container maxW="container.xl" px={2} py={1}>
                <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                    {/* Location Section */}
                    <Box
                        display="flex"
                        alignItems="center"
                        onClick={onLocationOpen}
                        cursor="pointer"
                        flex="1"
                        minW="0"
                    >
                        <Box pl={2} flex="1" minW="0">
                            {loading ? (
                                <Box fontSize="sm">
                                    <Spinner />
                                </Box>
                            ) : error ? (
                                <Box>
                                    <Box fontSize="sm" fontWeight="semibold" color="red.500">
                                        Location not found
                                    </Box>
                                </Box>
                            ) : (
                                <>
                                    <Box
                                        fontSize="lg"
                                        fontWeight="bold"
                                        isTruncated
                                        className="flex items-center text-sm mt-2"
                                    >
                                        <IoLocationOutline size={20} className="mr-1" />
                                        {pincode}
                                    </Box>
                                </>
                            )}
                        </Box>
                    </Box>

                    {/* Icons Section */}
                    <Box display="flex" alignItems="center" flexShrink={0}>
                        <Button
                            variant="ghost"
                            aria-label={t('common.notifications')}
                            position="relative"
                            px={2}
                        >
                            <LuBell size={22} />
                            <Box
                                as="span"
                                position="absolute"
                                top="1"
                                right="1"
                                bg="orange.400"
                                color="white"
                                fontSize="xs"
                                borderRadius="full"
                                h="4"
                                w="4"
                                display="flex"
                                alignItems="center"
                                justifyContent="center"
                                fontWeight="medium"
                            >
                                1
                            </Box>
                        </Button>
                        <Button
                            variant="ghost"
                            aria-label={t('common.cart')}
                            position="relative"
                            px={2}
                        >
                            <IoCartOutline size={24} />
                            <Box
                                as="span"
                                position="absolute"
                                top="1"
                                right="1"
                                bg="orange.400"
                                color="white"
                                fontSize="xs"
                                borderRadius="full"
                                h="4"
                                w="4"
                                display="flex"
                                alignItems="center"
                                justifyContent="center"
                                fontWeight="medium"
                            >
                                1
                            </Box>
                        </Button>
                    </Box>
                </Box>
            </Container>

            {/* Search Bar Section */}
            <Container maxW="container.xl" mx="auto" mt={6} onClick={onSearchOpen} cursor="pointer">
                <Box position="relative">
                    <InputGroup
                        size="lg"
                        boxShadow="lg"
                        _hover={{ transform: 'translateY(-2px)' }}
                        transition="all 0.3s ease"
                    >
                        <InputLeftElement pointerEvents="none" h="full" pl={4}>
                            <Search size={20} />
                        </InputLeftElement>
                        <Input
                            type="text"
                            placeholder={`Search for ${animatedWord}`}
                            aria-label="Search"
                            isReadOnly
                            bg="white"
                            fontSize={{ base: 'md', md: 'lg' }}
                            pl="3.5rem"
                            border="none"
                            _placeholder={{ color: 'gray.500' }}
                        />
                    </InputGroup>
                </Box>
            </Container>
        </header>
    ),
)
MobileView.displayName = 'MobileView'

const TabletView = memo(
    ({
        headerData,
        locality,
        pincode,
        t,
    }: {
        headerData: any
        locality?: string
        pincode?: string
        t: (key: string) => string
    }) => (
        <Box
            as="header"
            w="full"
            bg="white"
            shadow="sm"
            display={{ base: 'none', md: 'block', lg: 'none' }}
        >
            <Container
                maxW="container.xl"
                px={5}
                h="65px"
                display="flex"
                alignItems="center"
                justifyContent="space-between"
            >
                <Link href="/discovery">
                    <Box as="a" display="flex" alignItems="center" flexShrink={0} mr={3}>
                        <img
                            src={headerData.Image}
                            alt={headerData.logoText}
                            style={{ height: '36px', width: 'auto' }}
                        />
                    </Box>
                </Link>
                <Box
                    display="flex"
                    alignItems="center"
                    justifyContent="center" // horizontal center for entire group
                    gap={3}
                    height="100%" // ensure height exists (or give it a fixed height like 64px)
                >
                    {/* Pincode Section */}
                    <Box
                        display="flex"
                        alignItems="center"
                        gap={1}
                        color="blue.900"
                        fontWeight="medium"
                    >
                        <MapPin size={20} />
                        <Box as="span" fontSize="sm" whiteSpace="nowrap">
                            {pincode}
                        </Box>
                    </Box>

                    {/* Navigation Links */}
                    <Box
                        as="nav"
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        
                    >
                        {headerData.navLinks.map((link: any, index: number) => (
                            <Link key={index} href={link.href}>
                                <Box
                                    as="a"
                                    display="flex"
                                    alignItems="center"
                                    justifyContent="center"
                                    fontSize="sm"
                                    color="gray.700"
                                    _hover={{ color: 'blue.600' }}
                                    fontWeight="medium"
                                    whiteSpace="nowrap"
                                >
                                    {link.label}
                                </Box>
                            </Link>
                        ))}
                    </Box>

                    {/* Notification Bell */}
                    <Button
                        variant="ghost"
                        position="relative"
                        aria-label={t('common.notifications')}
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                    >
                        <LuBell size={20} />
                        <Box
                            as="span"
                            position="absolute"
                            top="0"
                            right="0"
                            display="flex"
                            alignItems="center"
                            justifyContent="center"
                            w="3"
                            h="3"
                            bg="orange.500"
                            color="white"
                            fontSize="xs"
                            fontWeight="bold"
                            borderRadius="full"
                        >
                            3
                        </Box>
                    </Button>

                    {/* Business Button */}
                    <Button
                        colorScheme="blue"
                        bg="blue.800"
                        _hover={{ bg: 'blue.900' }}
                        rounded="xl"
                        size="sm"
                        px={5}
                        py={2}
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                    >
                        {headerData.businessLink}
                    </Button>
                </Box>
            </Container>
        </Box>
    ),
)
TabletView.displayName = 'TabletView'

const DesktopView = memo(
    ({
        headerData,
        pincode,
        locality,
        t,
    }: {
        headerData: any
        pincode?: string
        locality?: string
        t: (key: string) => string
    }) => (
        <Box as="header" w="full" bg="white" shadow="sm" display={{ base: 'none', lg: 'block' }}>
            <Container
                maxW="container.3xl"
                h="72px"
                display="flex"
                alignItems="center"
                justifyContent="space-between"
                px={4}
            >
                {/* Logo */}
                <Link href="/discovery">
                    <Box as="a" display="flex" alignItems="center" flexShrink={0} mr={4}>
                        <img
                            src={headerData.Image}
                            alt={headerData.logoText}
                            style={{ height: '40px', width: 'auto' }}
                        />
                    </Box>
                </Link>

                {/* Right Section */}
                <Box display="flex" alignItems="center" justifyContent="center" gap={6} h="full">
                    {/* Pincode */}
                    <Box
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        gap={2}
                        color="blue.900"
                        fontWeight="medium"
                    >
                        <MapPin size={20} />
                        <Box as="span" fontSize="md" whiteSpace="nowrap">
                            {pincode}
                        </Box>
                    </Box>

                    {/* Nav Links */}
                    <Box
                        as="nav"
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        gap={6}
                    >
                        {headerData.navLinks.map((link: any, index: number) => (
                            <Link key={index} href={link.href}>
                                <Box
                                    as="a"
                                    fontSize="md"
                                    color="gray.800"
                                    _hover={{ color: 'blue.700' }}
                                    fontWeight="medium"
                                    whiteSpace="nowrap"
                                    display="flex"
                                    alignItems="center"
                                >
                                    {link.label}
                                </Box>
                            </Link>
                        ))}
                    </Box>

                    {/* Bell Icon */}
                    <Button
                        variant="ghost"
                        position="relative"
                        aria-label={t('common.notifications')}
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        p={0}
                    >
                        <LuBell size={20} />
                        <Box
                            as="span"
                            position="absolute"
                            top="0"
                            right="0"
                            display="flex"
                            alignItems="center"
                            justifyContent="center"
                            w="3"
                            h="3"
                            bg="orange.500"
                            color="white"
                            fontSize="xs"
                            fontWeight="bold"
                            borderRadius="full"
                        >
                            {Math.floor(Math.random() * 10)}
                        </Box>
                    </Button>

                    {/* Business CTA */}
                    <Button
                        colorScheme="blue"
                        bg="blue.800"
                        _hover={{ bg: 'blue.900' }}
                        rounded="xl"
                        size="md"
                        px={8}
                        py={3}
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                    >
                        {headerData.businessLink}
                    </Button>
                </Box>
            </Container>
        </Box>
    ),
)
DesktopView.displayName = 'DesktopView'

const Header: React.FC<HeaderProps> = ({ data }) => {
    // Hooks
    const { isMobile, isTablet, isMounted } = useDeviceContext()
    const { t } = useStaticText()
    const { openSearch } = useSearch()
    const { openLocation } = useLocation()
    const { locationDetails, loading, error, refetch } = useLocationLoader()

    // Memoized data
    const defaultData = useMemo(() => {
        const shopLabel = t('common.shop')
        const cartLabel = t('common.cart')
        const accountLabel = t('common.account')
        const businessLink = t('home.for_business')
        return {
            Image: '/assets/images/wify_logo.png',
            logoText: 'Wify',
            navLinks: [
                { href: '/shop', label: shopLabel },
                { href: '/cart', label: cartLabel },
                { href: '/profile', label: accountLabel },
            ],
            businessLink: businessLink,
        }
    }, [t])

    const headerData = data?.value || defaultData
    const streetAddress = locationDetails?.formattedAddress
    const locality = locationDetails?.neighborhood
    const pincode = locationDetails?.postalCode

    // Animated placeholder state
    const words = ['AC service', 'Kitchen service', 'products', 'cleaning', 'repair']
    const [animatedWord, setAnimatedWord] = useState('')
    const [wordIndex, setWordIndex] = useState(0)

    useEffect(() => {
        let display = ''
        let typing = true
        let idx = 0
        let timeout: NodeJS.Timeout
        const type = () => {
            const word = words[wordIndex]
            if (typing) {
                if (idx < word.length) {
                    display += word[idx]
                    setAnimatedWord(display)
                    idx++
                    timeout = setTimeout(type, 120)
                } else {
                    typing = false
                    timeout = setTimeout(type, 1000)
                }
            } else {
                if (idx > 0) {
                    display = display.slice(0, -1)
                    setAnimatedWord(display)
                    idx--
                    timeout = setTimeout(type, 60)
                } else {
                    typing = true
                    setWordIndex((prev) => (prev + 1) % words.length)
                    timeout = setTimeout(type, 500)
                }
            }
        }
        type()
        return () => clearTimeout(timeout)
    }, [wordIndex])

    // Render logic
    if (!isMounted) return null
    if (isMobile) {
        return (
            <MobileView
                onSearchOpen={openSearch}
                onLocationOpen={openLocation}
                animatedWord={animatedWord}
                locality={locality}
                streetAddress={streetAddress}
                pincode={pincode}
                t={t}
                error={error}
                loading={loading}
                refetch={refetch}
            />
        )
    }
    if (isTablet) return <TabletView headerData={headerData} locality={locality} pincode={pincode} t={t} />
    return <DesktopView headerData={headerData} pincode={pincode} locality={locality} t={t} />
}

export default Header
