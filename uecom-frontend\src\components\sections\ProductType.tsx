'use client'

import React from 'react'
import { motion } from 'framer-motion'
import Image from 'next/image'
import { ChevronLeft } from 'lucide-react'

interface ProductTypeProps {
    selectedCategory: { label: string; description: string }
    label: string
    onClose: () => void
}

const productTypes = [
    { label: 'Electrical & Appliances', image: '/assets/images/fan.png', selected: true },
    { label: 'Furniture', image: '/assets/images/fan.png' },
    { label: 'Smart Homes', image: '/assets/images/fan.png' },
    { label: 'Appliances', image: '/assets/images/fan.png' },
    { label: 'Water Purifier', image: '/assets/images/fan.png' },
    { label: 'Hardware', image: '/assets/images/fan.png' },
]

const ProductType: React.FC<ProductTypeProps> = ({ selectedCategory, label, onClose }) => {
    return (
        <motion.div
            initial={{ x: 300, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: 300, opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="bg-white shadow-md rounded-lg p-4 w-full sm:w-[400px]"
        >
            <button
                onClick={onClose}
                className="flex items-center gap-2 text-sm text-gray-600 hover:text-black mb-4"
            >
                <ChevronLeft className="w-5 h-5" />
                <span className="text-xl font-bold text-[#1E293B]">{selectedCategory.label}</span>
            </button>

            <div className="grid grid-cols-3 gap-2 bg-white rounded-xl p-2 shadow-sm">
                {productTypes.map(({ label, image, selected }) => (
                    <div
                        key={label}
                        className={`flex flex-col items-center justify-center gap-2 p-2 rounded-lg transition
              ${selected ? 'text-blue-600 font-semibold' : 'text-gray-800 hover:text-blue-600'}`}
                    >
                        <div className="w-12 h-12 relative">
                            <Image src={image} alt={label} layout="fill" objectFit="contain" />
                        </div>
                        <div className="font-medium text-[11px] text-center">{label}</div>
                    </div>
                ))}
            </div>
        </motion.div>
    )
}

export default ProductType
