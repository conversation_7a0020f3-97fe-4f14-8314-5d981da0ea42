import {
    Box,
    Container,
    Input,
    InputGroup,
    InputLeftElement,
    Text,
    keyframes,
} from '@chakra-ui/react'
import { MapPin, Search } from 'lucide-react'
import { SearchServiceSection } from '../../types'

interface SearchServiceProps {
    data: SearchServiceSection
}

const pulse = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
`

export default function SearchService({ data }: SearchServiceProps) {
    const postalCode = data.value.location

    // Shared Input Component
    const SearchInput = ({ placeholder }: { placeholder: string }) => (
        <form action="/search" method="GET" style={{ width: '100%' }}>
            <InputGroup
                size="lg"
                boxShadow="lg"
                _hover={{ transform: 'translateY(-2px)' }}
                transition="all 0.3s ease"
            >
                <InputLeftElement pointerEvents="none" h="full" pl={4}>
                    <Search size={20} className="text-blue-500" />
                </InputLeftElement>
                <Input
                    type="text"
                    placeholder={placeholder}
                    aria-label={placeholder}
                    bg="white"
                    rounded="xl"
                    fontSize={{ base: 'md', md: 'lg' }}
                    h="56px"
                    pl="3.5rem"
                    _placeholder={{ color: 'gray.500' }}
                    _focus={{
                        borderColor: 'blue.400',
                        boxShadow: '0 0 0 1px var(--chakra-colors-blue-400)',
                    }}
                    _hover={{
                        borderColor: 'blue.300',
                    }}
                />
            </InputGroup>
        </form>
    )

    const TabletView = () => (
        <Container
            maxW="container.xl"
            mx="auto"
            px={6}
            py={10}
            border="none"
            bgGradient="linear(to-b, white, #f0f9ff)"
            position="relative"
            zIndex="10"
            display={{ base: 'none', md: 'block', lg: 'none' }}
            boxShadow="sm"
        >
            <Box textAlign="center" maxW="2xl" mx="auto" mb={8}>
                <Text color="blue.600" fontWeight="medium" fontSize="md" mb={6}>
                    {data.value.subheading}
                </Text>
            </Box>
            <Box maxW="3xl" mx="auto" px={4}>
                <SearchInput placeholder={data.value.searchPlaceholder} />
            </Box>
        </Container>
    )

    const DesktopView = () => (
        <Container
            maxW="container.xl"
            mx="auto"
            px={8}
            py={16}
            border="none"
            bgGradient="linear(to-b, white, #f0f9ff)"
            position="relative"
            zIndex="10"
            display={{ base: 'none', lg: 'block' }}
            boxShadow="sm"
        >
            <Box textAlign="center" maxW="4xl" mx="auto" mb={12}>
                <Text color="blue.600" fontWeight="medium" fontSize="lg" mb={8}>
                    {data.value.subheading}
                </Text>
            </Box>
            <Box maxW="4xl" mx="auto" px={4}>
                <SearchInput placeholder={data.value.searchPlaceholder} />
            </Box>
        </Container>
    )

    return (
        <Box position="relative" as="section" aria-label="Search services">
            <TabletView />
            <DesktopView />
        </Box>
    )
}
