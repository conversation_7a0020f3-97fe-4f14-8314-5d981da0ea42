'use client'

import React, { useState } from 'react'
import { ServiceCategoriesSection } from '../../types'
import { useDeviceContext } from '../../providers/DeviceProvider'
import { AnimatePresence } from 'framer-motion'
import ProductType from './ProductType' 

interface ServiceCategoriesProps {
    data: ServiceCategoriesSection
}

const ServiceCategories: React.FC<ServiceCategoriesProps> = ({ data }) => {
    const { label, value } = data
    const { categories } = value
    const [selectedCategory, setSelectedCategory] = useState<any>(null)
    const { isMobile, isTablet, isMounted } = useDeviceContext()

    if (!categories || categories.length === 0) {
        return null
    }

    const handleCategoryClick = (category: any) => {
        setSelectedCategory(category)
    }

    const MobileView = () => (
        <div className="px-4 mt-8 block sm:block md:hidden">
            <h2 className="text-sm font-semibold text-[#1E293B] mb-4">{label}</h2>
            <div className="grid grid-cols-3 gap-3">
                {categories.map((category) => (
                    <div
                        key={category.label}
                        className="bg-gray-100 p-4 rounded-xl shadow-sm border hover:shadow-md transition-shadow relative h-[100px] overflow-hidden cursor-pointer"
                        onClick={() => handleCategoryClick(category)}
                    >
                        <div className="absolute -top-2 -right-2">
                            <div className="bg-gray-300 w-16 h-16 rounded-full flex items-center justify-center p-2">
                                <div className="relative top-3 right-3">
                                    <img
                                        className="w-14 h-14 object-contain"
                                        src={category.image}
                                        alt={`${category.label} service category icon`}
                                        loading="lazy"
                                        onError={(e) => {
                                            const target = e.target as HTMLImageElement
                                            target.src = '/images/fallback-service-icon.svg'
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                        <div className="absolute bottom-1 left-2 ">
                            <h3 className="text-[#1E293B] text-xs font-medium mt-2">
                                {category.label}
                            </h3>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    )

    const TabletView = () => (
        <div className="px-6 mt-6 hidden md:block lg:hidden">
            <h2 className="text-lg font-bold text-[#1E293B] mb-5">{label}</h2>
            <div className="grid grid-cols-4 gap-4">
                {categories.map((category) => (
                    <div
                        key={category.label}
                        className="bg-gray-100 p-4 rounded-xl shadow-sm hover:shadow-md relative h-[120px] overflow-hidden cursor-pointer"
                        onClick={() => handleCategoryClick(category)}
                    >
                        <div className="absolute -top-2 -right-2">
                            <div className="bg-gray-300 w-20 h-20 rounded-full flex items-center justify-center p-2">
                                <div className="relative top-4 right-4">
                                    <img
                                        className="w-16 h-16 object-contain"
                                        src={category.image}
                                        alt={`${category.label} service category icon`}
                                        loading="lazy"
                                        onError={(e) => {
                                            const target = e.target as HTMLImageElement
                                            target.src = '/images/fallback-service-icon.svg'
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                        <div className="absolute bottom-2 left-2">
                            <h3 className="text-[#1E293B] text-sm font-medium">{category.label}</h3>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    )

    const DesktopView = () => (
        <div className="px-10 mt-2 hidden lg:block">
            <h2 className="text-l font-bold text-[#1E293B] mb-6">{label}</h2>
            <div className="grid grid-cols-6 gap-4">
                {categories.map((category) => (
                    <div
                        key={category.label}
                        className="bg-gray-100 p-4 rounded-xl shadow-sm hover:shadow-md relative h-[120px] overflow-hidden cursor-pointer"
                        onClick={() => handleCategoryClick(category)}
                    >
                        <div className="absolute -top-2 -right-2">
                            <div className="bg-gray-300 w-22 h-22 rounded-full flex items-center justify-center p-2">
                                <div className="relative top-5 right-5">
                                    <img
                                        className="w-20 h-20 object-contain"
                                        src={category.image}
                                        alt={`${category.label} service category icon`}
                                        loading="lazy"
                                        onError={(e) => {
                                            const target = e.target as HTMLImageElement
                                            target.src = '/images/fallback-service-icon.svg'
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                        <div className="absolute bottom-2 left-3">
                            <h3 className="text-[#1E293B] text-sm font-medium">{category.label}</h3>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    )

    if (!isMounted) return null
    if (selectedCategory) {
        // This would be the detailed view for a selected category
        return (
            <AnimatePresence mode='wait'>
                {selectedCategory && (
                    <ProductType
                        key="product-type"
                        selectedCategory={selectedCategory}
                        label={label}
                        onClose={() => setSelectedCategory(null)}
                    />
                )}
            </AnimatePresence>
        )
    }

    if (isMobile) return <MobileView />
    if (isTablet) return <TabletView />
    return <DesktopView />
}
export default ServiceCategories
