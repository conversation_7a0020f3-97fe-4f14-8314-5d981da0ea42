import React from 'react'
import { Building2, CheckCircle2 } from 'lucide-react'
import { B2BRegistrationSection } from '../../types'
import { useDeviceContext } from '../../providers/DeviceProvider'

interface B2BRegistrationProps {
    data: B2BRegistrationSection
}

const B2BRegistration: React.FC<B2BRegistrationProps> = ({ data }) => {
    const { label, value } = data
    const { isMobile, isTablet, isMounted } = useDeviceContext()

    const MobileView = () => (
        <div className="px-2 mt-10 block sm:block md:hidden">
            <div
                className="rounded-xl overflow-hidden relative border border-solid border-orange-500"
                style={{
                    background:
                        'linear-gradient(145deg, #0D2D7D, #397AF2, #90B8FF, #397AF2, #183F85)',
                }}
            >
                <div className="absolute top-2 right-2">
                    <div className="w-10 h-10 bg-white/70 rounded-full flex items-center justify-center">
                        <Building2 className="w-6 h-6 text-blue-950" />
                    </div>
                </div>
                <div className="absolute -top-4 -right-4">
                    <div className="w-20 h-20 bg-white/40 rounded-full animate-pulse"></div>
                </div>

                <div className="p-3 text-white">
                    <h2 className="text-xl font-bold mb-1">{label}</h2>
                    <p className="text-white/90 text-xs font-medium mb-6 mt-4">{value.subheading}</p>

                    <div className="grid grid-cols-2 gap-1 mb-4">
                        {value.features?.map((features: { label: string }) => (
                            <div
                                key={features.label}
                                className="flex items-center gap-1 bg-white/40 rounded-lg p-3"
                            >
                                <CheckCircle2 className="w-4 h-4 text-blue-950 flex-shrink-0" />
                                <span className="text-blue-primary font-semibold text-[11px]">
                                    {features.label}
                                </span>
                            </div>
                        ))}
                    </div>

                    <button className="w-full bg-white text-blue-primary rounded-xl text-sm font-medium hover:bg-white/90 transition-colors">
                        {value.buttonText}
                    </button>
                </div>
            </div>
        </div>
    )

    const TabletView = () => (
        <div className="px-6 mt-12 hidden md:block lg:hidden">
            <div
                className="rounded-3xl overflow-hidden relative border border-solid border-orange-500"
                style={{
                    background:
                        'linear-gradient(135deg, #0052D4, #4364F7, #a5a5a5, #4364F7, #6FB1FC)',
                }}
            >
                <div className="absolute top-3 right-3">
                    <div className="w-14 h-14 bg-white/70 rounded-full flex items-center justify-center">
                        <Building2 className="w-7 h-7 text-blue-950" />
                    </div>
                </div>
                <div className="absolute -top-5 -right-5">
                    <div className="w-28 h-28 bg-white/20 rounded-full animate-pulse"></div>
                </div>

                <div className="p-8 text-white">
                    <h2 className="text-2xl font-bold mb-2">{label}</h2>
                    <p className="text-white/90 text-lg mb-7">{value.subheading}</p>

                    <div className="grid grid-cols-2 gap-4 mb-7">
                        {value.features?.map((features: { label: string }) => (
                            <div
                                key={features.label}
                                className="flex items-center gap-3 bg-white/60 rounded-lg p-4"
                            >
                                <CheckCircle2 className="w-6 h-6 text-blue-950 flex-shrink-0" />
                                <span className="text-blue-950 font-semibold text-base">
                                    {features.label}
                                </span>
                            </div>
                        ))}
                    </div>

                    <button className="w-full bg-white text-blue-600 rounded-xl py-3 font-semibold text-lg hover:bg-white/90 transition-colors">
                        {value.buttonText}
                    </button>
                </div>
            </div>
        </div>
    )

    const DesktopView = () => (
        <div className="px-8 mt-16 hidden lg:block">
            <div
                className="rounded-3xl overflow-hidden relative border border-solid border-orange-500 max-w-5xl mx-auto"
                style={{
                    background:
                        'linear-gradient(135deg, #0052D4, #4364F7, #a5a5a5, #4364F7, #6FB1FC)',
                }}
            >
                <div className="absolute top-4 right-4">
                    <div className="w-16 h-16 bg-white/70 rounded-full flex items-center justify-center">
                        <Building2 className="w-8 h-8 text-blue-950" />
                    </div>
                </div>
                <div className="absolute -top-6 -right-6">
                    <div className="w-32 h-32 bg-white/20 rounded-full animate-pulse"></div>
                </div>

                <div className="p-10 text-white">
                    <h2 className="text-3xl font-bold mb-3">{label}</h2>
                    <p className="text-white/90 text-xl mb-8 max-w-3xl">{value.subheading}</p>

                    <div className="grid grid-cols-3 gap-5 mb-8">
                        {value.features?.map((features: { label: string }) => (
                            <div
                                key={features.label}
                                className="flex items-center gap-3 bg-white/60 rounded-lg p-4"
                            >
                                <CheckCircle2 className="w-6 h-6 text-blue-950 flex-shrink-0" />
                                <span className="text-blue-950 font-semibold text-base">
                                    {features.label}
                                </span>
                            </div>
                        ))}
                    </div>

                    <button className="bg-white text-blue-600 rounded-xl py-4 px-8 font-semibold text-lg hover:bg-white/90 transition-colors">
                        {value.buttonText}
                    </button>
                </div>
            </div>
        </div>
    )

    if (!isMounted) return null
    if (isMobile) return <MobileView />
    // if (isTablet) return <TabletView />
    // return <DesktopView />
}

export default B2BRegistration
