import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import Modal from './Modal'

// <PERSON>ck createPortal to render in the same container
jest.mock('react-dom', () => ({
    ...jest.requireActual('react-dom'),
    createPortal: (node: React.ReactNode) => node,
}))

describe('Modal', () => {
    const defaultProps = {
        isOpen: true,
        onClose: jest.fn(),
        title: 'Test Modal',
        children: <div>Modal content</div>,
    }

    beforeEach(() => {
        jest.clearAllMocks()
        // Reset body overflow style
        document.body.style.overflow = 'unset'
    })

    it('renders modal when isOpen is true', () => {
        render(<Modal {...defaultProps} />)
        
        expect(screen.getByRole('dialog')).toBeInTheDocument()
        expect(screen.getByText('Test Modal')).toBeInTheDocument()
        expect(screen.getByText('Modal content')).toBeInTheDocument()
    })

    it('does not render modal when isOpen is false', () => {
        render(<Modal {...defaultProps} isOpen={false} />)
        
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument()
    })

    it('calls onClose when close button is clicked', () => {
        const onClose = jest.fn()
        render(<Modal {...defaultProps} onClose={onClose} />)
        
        const closeButton = screen.getByLabelText('Close modal')
        fireEvent.click(closeButton)
        
        expect(onClose).toHaveBeenCalledTimes(1)
    })

    it('calls onClose when escape key is pressed', () => {
        const onClose = jest.fn()
        render(<Modal {...defaultProps} onClose={onClose} />)
        
        fireEvent.keyDown(document, { key: 'Escape' })
        
        expect(onClose).toHaveBeenCalledTimes(1)
    })

    it('calls onClose when overlay is clicked', () => {
        const onClose = jest.fn()
        render(<Modal {...defaultProps} onClose={onClose} />)
        
        const overlay = screen.getByRole('dialog').firstChild as HTMLElement
        fireEvent.click(overlay)
        
        expect(onClose).toHaveBeenCalledTimes(1)
    })

    it('does not call onClose when modal content is clicked', () => {
        const onClose = jest.fn()
        render(<Modal {...defaultProps} onClose={onClose} />)
        
        const modalContent = screen.getByText('Modal content')
        fireEvent.click(modalContent)
        
        expect(onClose).not.toHaveBeenCalled()
    })

    it('does not show close button when showCloseButton is false', () => {
        render(<Modal {...defaultProps} showCloseButton={false} />)
        
        expect(screen.queryByLabelText('Close modal')).not.toBeInTheDocument()
    })

    it('applies correct size classes', () => {
        const { rerender } = render(<Modal {...defaultProps} size="sm" />)
        expect(screen.getByRole('dialog').lastChild).toHaveClass('max-w-md')
        
        rerender(<Modal {...defaultProps} size="lg" />)
        expect(screen.getByRole('dialog').lastChild).toHaveClass('max-w-2xl')
        
        rerender(<Modal {...defaultProps} size="xl" />)
        expect(screen.getByRole('dialog').lastChild).toHaveClass('max-w-4xl')
    })

    it('prevents body scroll when modal is open', () => {
        render(<Modal {...defaultProps} />)
        
        expect(document.body.style.overflow).toBe('hidden')
    })

    it('restores body scroll when modal is closed', () => {
        const { rerender } = render(<Modal {...defaultProps} />)
        expect(document.body.style.overflow).toBe('hidden')
        
        rerender(<Modal {...defaultProps} isOpen={false} />)
        expect(document.body.style.overflow).toBe('unset')
    })
})
