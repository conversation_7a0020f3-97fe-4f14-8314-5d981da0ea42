'use client'

import React, { useEffect, useRef, useCallback } from 'react'
import { createPortal } from 'react-dom'
import { X } from 'lucide-react'
import { cn } from '../../../lib/utils/cn'

interface ModalProps {
    /**
     * Whether the modal is open
     */
    isOpen: boolean

    /**
     * Function to call when the modal should be closed
     */
    onClose: () => void

    /**
     * Modal title for accessibility
     */
    title: string

    /**
     * Modal content
     */
    children: React.ReactNode

    /**
     * Additional CSS classes for the modal content
     */
    className?: string

    /**
     * Whether to show the close button
     * @default true
     */
    showCloseButton?: boolean

    /**
     * Size variant of the modal
     * @default 'default'
     */
    size?: 'sm' | 'default' | 'lg' | 'xl' | 'full'

    /**
     * Whether the modal should close when clicking the overlay
     * @default true
     */
    closeOnOverlayClick?: boolean

    /**
     * Whether the modal should close when pressing Escape
     * @default true
     */
    closeOnEscape?: boolean
}

const sizeVariants = {
    sm: 'max-w-md',
    default: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-full mx-4',
}

/**
 * Accessible Modal component with focus trapping and keyboard navigation
 *
 * Features:
 * - Focus trapping
 * - Escape key to close
 * - Click outside to close
 * - Smooth animations
 * - Responsive sizing
 * - Accessibility compliant
 */
const Modal: React.FC<ModalProps> = ({
    isOpen,
    onClose,
    title,
    children,
    className,
    showCloseButton = true,
    size = 'default',
    closeOnOverlayClick = true,
    closeOnEscape = true,
}) => {
    const modalRef = useRef<HTMLDivElement>(null)
    const previousActiveElement = useRef<HTMLElement | null>(null)

    // Handle escape key
    const handleEscape = useCallback(
        (event: KeyboardEvent) => {
            if (closeOnEscape && event.key === 'Escape') {
                onClose()
            }
        },
        [closeOnEscape, onClose],
    )

    // Handle overlay click
    const handleOverlayClick = useCallback(
        (event: React.MouseEvent<HTMLDivElement>) => {
            if (closeOnOverlayClick && event.target === event.currentTarget) {
                onClose()
            }
        },
        [closeOnOverlayClick, onClose],
    )

    // Focus trapping
    const handleKeyDown = useCallback((event: KeyboardEvent) => {
        if (!modalRef.current) return

        const focusableElements = modalRef.current.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
        )
        const firstElement = focusableElements[0] as HTMLElement
        const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement

        if (event.key === 'Tab') {
            if (event.shiftKey) {
                if (document.activeElement === firstElement) {
                    event.preventDefault()
                    lastElement?.focus()
                }
            } else {
                if (document.activeElement === lastElement) {
                    event.preventDefault()
                    firstElement?.focus()
                }
            }
        }
    }, [])

    // Effect for managing focus and event listeners
    useEffect(() => {
        if (isOpen) {
            // Store the currently focused element
            previousActiveElement.current = document.activeElement as HTMLElement

            // Add event listeners
            document.addEventListener('keydown', handleEscape)
            document.addEventListener('keydown', handleKeyDown)

            // Prevent body scroll
            document.body.style.overflow = 'hidden'

            // Focus the modal
            setTimeout(() => {
                modalRef.current?.focus()
            }, 100)
        } else {
            // Remove event listeners
            document.removeEventListener('keydown', handleEscape)
            document.removeEventListener('keydown', handleKeyDown)

            // Restore body scroll
            document.body.style.overflow = 'unset'

            // Restore focus to the previously focused element
            if (previousActiveElement.current) {
                previousActiveElement.current.focus()
            }
        }

        return () => {
            document.removeEventListener('keydown', handleEscape)
            document.removeEventListener('keydown', handleKeyDown)
            document.body.style.overflow = 'unset'
        }
    }, [isOpen, handleEscape, handleKeyDown])

    // Don't render if not open
    if (!isOpen) return null

    // Create portal to render modal at the end of body
    return createPortal(
        <div
            className="fixed inset-0 z-50 flex items-center justify-center"
            role="dialog"
            aria-modal="true"
            aria-labelledby="modal-title"
        >
            {/* Overlay */}
            <div
                className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300"
                onClick={handleOverlayClick}
                aria-hidden="true"
            />

            {/* Modal Content */}
            <div
                ref={modalRef}
                className={cn(
                    'relative bg-white rounded-lg shadow-xl transition-all duration-300 transform',
                    'animate-fade-in animate-slide-in-from-bottom animate-zoom-in',
                    'w-full max-h-[90vh] overflow-hidden',
                    sizeVariants[size],
                    className,
                )}
                tabIndex={-1}
            >
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-200">
                    <h2 id="modal-title" className="text-lg font-semibold text-gray-900">
                        {title}
                    </h2>

                    {showCloseButton && (
                        <button
                            onClick={onClose}
                            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors"
                            aria-label="Close modal"
                        >
                            <X size={20} />
                        </button>
                    )}
                </div>

                {/* Content */}
                <div className="overflow-y-auto max-h-[calc(90vh-80px)]">{children}</div>
            </div>
        </div>,
        document.body,
    )
}

export default Modal
