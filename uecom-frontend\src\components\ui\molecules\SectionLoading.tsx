'use client'

import React from 'react'
import { useDevice } from '@/hooks/useDevice'

interface SectionLoadingProps {
    /**
     * Number of placeholder sections to show
     * @default 3
     */
    count?: number

    /**
     * Whether to show header placeholder
     * @default true
     */
    showHeader?: boolean

    /**
     * Whether to show footer placeholder
     * @default true
     */
    showFooter?: boolean
}

/**
 * Section loading component that shows placeholder content while sections are loading
 * Adapts to different device sizes and can show different numbers of sections
 */
const SectionLoading: React.FC<SectionLoadingProps> = ({
    count = 3,
    showHeader = true,
    showFooter = true,
}) => {
    const { isMobile } = useDevice()

    return (
        <div className="w-full animate-pulse">
            {/* Header placeholder */}
            {showHeader && (
                <div className="bg-gray-100 w-full h-16 mb-4 flex items-center justify-between px-4">
                    <div className="bg-gray-200 h-8 w-32 rounded"></div>
                    <div className="flex space-x-4">
                        <div className="bg-gray-200 h-8 w-20 rounded"></div>
                        <div className="bg-gray-200 h-8 w-20 rounded"></div>
                    </div>
                </div>
            )}

            {/* Content placeholders */}
            <div className="px-4 space-y-8 my-8">
                {/* Generate the specified number of section placeholders */}
                {Array.from({ length: count }).map((_, index) => (
                    <div key={index} className="space-y-4">
                        <div className="bg-gray-200 h-6 w-48 rounded"></div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {Array.from({ length: isMobile ? 2 : 6 }).map((_, idx) => (
                                <div
                                    key={idx}
                                    className="bg-gray-100 h-32 rounded flex items-center justify-center"
                                >
                                    <div className="bg-gray-200 h-16 w-16 rounded-full"></div>
                                </div>
                            ))}
                        </div>
                    </div>
                ))}
            </div>

            {/* Footer placeholder */}
            {showFooter && (
                <div className="bg-gray-100 w-full h-48 mt-auto">
                    <div className="max-w-screen-xl mx-auto px-4 py-8 grid grid-cols-2 md:grid-cols-4 gap-8">
                        {Array.from({ length: 4 }).map((_, index) => (
                            <div key={index} className="space-y-4">
                                <div className="bg-gray-200 h-4 w-24 rounded"></div>
                                <div className="space-y-2">
                                    <div className="bg-gray-200 h-3 w-20 rounded"></div>
                                    <div className="bg-gray-200 h-3 w-16 rounded"></div>
                                    <div className="bg-gray-200 h-3 w-24 rounded"></div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    )
}

export default SectionLoading
