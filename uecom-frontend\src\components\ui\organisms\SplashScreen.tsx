'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON>Lef<PERSON>, ArrowRight } from 'lucide-react'
import Image from 'next/image'
import {
    Box,
    Button,
    Container,
    Heading,
    Text,
    VStack,
    IconButton,
    useColorModeValue,
} from '@chakra-ui/react'

interface Screen {
    id: number
    title: string
    description: string
    icon: string
}

interface SplashScreenProps {
    screens: Screen[]
}

const SCREEN_DELAY_MS = 3000

export default function SplashScreen({ screens }: SplashScreenProps) {
    const router = useRouter()
    const [currentScreen, setCurrentScreen] = useState(0)
    const [direction, setDirection] = useState<'left' | 'right'>('right')
    const [imgError, setImgError] = useState(false)
    const [splashChecked, setSplashChecked] = useState(false)
    const [shouldShowSplash, setShouldShowSplash] = useState(true)

    const bgGradient = useColorModeValue(
        'linear(to-b, white, blue.50)',
        'linear(to-b, gray.800, blue.900)',
    )
    const buttonBg = useColorModeValue('white', 'gray.700')
    const buttonHoverBg = useColorModeValue('blue.50', 'gray.600')
    const textColor = useColorModeValue('blue.900', 'white')
    const descriptionColor = useColorModeValue('blue.700', 'blue.200')

    // Helper function to check if user is logged in
    const isUserLoggedIn = useCallback(() => {
        return (
            localStorage.getItem('isVerified') === 'true' ||
            !!document.cookie.includes('session_token')
        )
    }, [])

    const redirectToAuth = useCallback(() => {
        router.replace('/auth')
    }, [router])

    const redirectToDiscovery = useCallback(() => {
        router.replace('/discovery')
    }, [router])

    // Check localStorage and authentication status only once at start
    useEffect(() => {
        const alreadySeen = localStorage.getItem('is_splash_show_done') === 'true'

        if (alreadySeen) {
            setShouldShowSplash(false)
            // If user is logged in, redirect to discovery page, otherwise to auth
            if (isUserLoggedIn()) {
                redirectToDiscovery()
            } else {
                redirectToAuth()
            }
        } else {
            setSplashChecked(true)
        }
    }, [redirectToAuth, redirectToDiscovery, isUserLoggedIn])

    // Auto-advance screen or redirect
    useEffect(() => {
        if (!shouldShowSplash || screens.length === 0) return

        const timer = setTimeout(() => {
            if (currentScreen < screens.length - 1) {
                setDirection('right')
                setCurrentScreen((prev) => prev + 1)
            } else {
                localStorage.setItem('is_splash_show_done', 'true')
                // Check if user is logged in before redirecting
                if (isUserLoggedIn()) {
                    redirectToDiscovery()
                } else {
                    redirectToAuth()
                }
            }
        }, SCREEN_DELAY_MS)

        return () => clearTimeout(timer)
    }, [
        currentScreen,
        screens.length,
        shouldShowSplash,
        redirectToAuth,
        redirectToDiscovery,
        isUserLoggedIn,
    ])

    const handleSkip = () => {
        localStorage.setItem('is_splash_show_done', 'true')
        // Check if user is logged in before redirecting
        if (isUserLoggedIn()) {
            redirectToDiscovery()
        } else {
            redirectToAuth()
        }
    }

    const nextScreen = () => {
        if (currentScreen < screens.length - 1) {
            setDirection('right')
            setCurrentScreen((prev) => prev + 1)
            setImgError(false)
        }
    }

    const prevScreen = () => {
        if (currentScreen > 0) {
            setDirection('left')
            setCurrentScreen((prev) => prev - 1)
            setImgError(false)
        }
    }

    const current = screens[currentScreen]

    // 💡 Don't render anything if splash was already shown
    if (!shouldShowSplash || !splashChecked) return null

    return (
        <Box
            position="relative"
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            height="100vh"
            bgGradient={bgGradient}
            px={4}
        >
            <Button
                position="absolute"
                top={4}
                right={4}
                zIndex={50}
                colorScheme="blue"
                variant="ghost"
                size="lg"
                onClick={handleSkip}
                aria-label="Skip Onboarding"
            >
                Skip
            </Button>

            <AnimatePresence mode="wait" custom={direction}>
                <Box
                    position="relative"
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                    width="full"
                    height="100vh"
                >
                    {currentScreen > 0 && (
                        <IconButton
                            position="absolute"
                            left={{ base: 4, sm: 6 }}
                            aria-label="Previous Screen"
                            icon={<ArrowLeft size={24} />}
                            onClick={prevScreen}
                            bg={buttonBg}
                            _hover={{ bg: buttonHoverBg }}
                            shadow="lg"
                            rounded="full"
                            zIndex={50}
                        />
                    )}

                    <motion.div
                        key={current.id}
                        custom={direction}
                        initial={{ opacity: 0, x: direction === 'right' ? 100 : -100 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: direction === 'right' ? -100 : 100 }}
                        transition={{ type: 'spring', stiffness: 100, damping: 20 }}
                    >
                        <Container maxW="container.sm" centerContent px={4}>
                            <VStack spacing={6} align="center">
                                <Box
                                    p={4}
                                    bg={buttonBg}
                                    rounded="full"
                                    shadow="lg"
                                    width={{ base: '28', sm: '32' }}
                                    height={{ base: '28', sm: '32' }}
                                    display="flex"
                                    alignItems="center"
                                    justifyContent="center"
                                >
                                    <Image
                                        src={imgError ? '/icons/placeholder.png' : current.icon}
                                        alt={current.title}
                                        width={128}
                                        height={128}
                                        style={{
                                            width: '100%',
                                            height: '100%',
                                            objectFit: 'contain',
                                        }}
                                        onError={() => setImgError(true)}
                                        priority
                                    />
                                </Box>

                                <Heading
                                    as="h1"
                                    size={{ base: 'xl', sm: '2xl' }}
                                    color={textColor}
                                    textAlign="center"
                                >
                                    {current.title}
                                </Heading>
                                <Text
                                    color={descriptionColor}
                                    textAlign="center"
                                    maxW={{ base: 'xs', sm: 'sm' }}
                                    px={2}
                                >
                                    {current.description}
                                </Text>
                            </VStack>
                        </Container>
                    </motion.div>

                    {currentScreen < screens.length - 1 && (
                        <IconButton
                            position="absolute"
                            right={{ base: 4, sm: 6 }}
                            aria-label="Next Screen"
                            icon={<ArrowRight size={24} />}
                            onClick={nextScreen}
                            bg={buttonBg}
                            _hover={{ bg: buttonHoverBg }}
                            shadow="lg"
                            rounded="full"
                            zIndex={50}
                        />
                    )}
                </Box>
            </AnimatePresence>
        </Box>
    )
}
