/**
 * Tests for useServiceCategories hook
 */

import { renderHook, waitFor } from '@testing-library/react';
import { useServiceCategories } from '../useServiceCategories';
import { serviceCategoryService } from '../../services/serviceCategoryService';

// Mock the service
jest.mock('../../services/serviceCategoryService');
const mockServiceCategoryService = serviceCategoryService as jest.Mocked<typeof serviceCategoryService>;

describe('useServiceCategories', () => {
    const mockServiceCategories = [
        {
            id: '1',
            label: 'Installation',
            description: 'Professional installation services',
            image: 'https://example.com/image1.jpg',
            iconName: 'drill',
        },
        {
            id: '2',
            label: 'Repair',
            description: 'Professional repair services',
            image: 'https://example.com/image2.jpg',
            iconName: 'wrench',
        },
    ];

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should fetch service categories on mount when autoFetch is true', async () => {
        mockServiceCategoryService.fetchServiceCategories.mockResolvedValue(mockServiceCategories);

        const { result } = renderHook(() => useServiceCategories(true));

        expect(result.current.loading).toBe(true);
        expect(result.current.serviceCategories).toEqual([]);
        expect(result.current.error).toBeNull();

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.serviceCategories).toEqual(mockServiceCategories);
        expect(result.current.error).toBeNull();
        expect(mockServiceCategoryService.fetchServiceCategories).toHaveBeenCalledTimes(1);
    });

    it('should not fetch service categories on mount when autoFetch is false', () => {
        mockServiceCategoryService.fetchServiceCategories.mockResolvedValue(mockServiceCategories);

        const { result } = renderHook(() => useServiceCategories(false));

        expect(result.current.loading).toBe(false);
        expect(result.current.serviceCategories).toEqual([]);
        expect(result.current.error).toBeNull();
        expect(mockServiceCategoryService.fetchServiceCategories).not.toHaveBeenCalled();
    });

    it('should handle fetch errors', async () => {
        const errorMessage = 'Failed to fetch service categories';
        mockServiceCategoryService.fetchServiceCategories.mockRejectedValue(new Error(errorMessage));

        const { result } = renderHook(() => useServiceCategories(true));

        expect(result.current.loading).toBe(true);

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.serviceCategories).toEqual([]);
        expect(result.current.error).toBe(errorMessage);
    });

    it('should refetch data when refetch is called', async () => {
        mockServiceCategoryService.fetchServiceCategories.mockResolvedValue(mockServiceCategories);

        const { result } = renderHook(() => useServiceCategories(false));

        await waitFor(() => {
            result.current.refetch();
        });

        expect(result.current.loading).toBe(true);

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.serviceCategories).toEqual(mockServiceCategories);
        expect(mockServiceCategoryService.fetchServiceCategories).toHaveBeenCalledTimes(1);
    });

    it('should clear cache and refetch when clearCache is called', async () => {
        mockServiceCategoryService.clearCache.mockResolvedValue();
        mockServiceCategoryService.fetchServiceCategories.mockResolvedValue(mockServiceCategories);

        const { result } = renderHook(() => useServiceCategories(false));

        await waitFor(() => {
            result.current.clearCache();
        });

        expect(result.current.loading).toBe(true);

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(mockServiceCategoryService.clearCache).toHaveBeenCalledTimes(1);
        expect(mockServiceCategoryService.fetchServiceCategories).toHaveBeenCalledTimes(1);
        expect(result.current.serviceCategories).toEqual(mockServiceCategories);
    });

    it('should handle clearCache errors', async () => {
        const errorMessage = 'Failed to clear cache';
        mockServiceCategoryService.clearCache.mockRejectedValue(new Error(errorMessage));

        const { result } = renderHook(() => useServiceCategories(false));

        await waitFor(() => {
            result.current.clearCache();
        });

        await waitFor(() => {
            expect(result.current.loading).toBe(false);
        });

        expect(result.current.error).toBe(errorMessage);
    });
});
