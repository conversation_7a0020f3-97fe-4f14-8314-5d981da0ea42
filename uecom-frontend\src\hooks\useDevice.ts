'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import { useMediaQuery } from 'react-responsive'
import { DeviceType, DEVICE_BREAKPOINTS } from '../lib/constants/breakpoints'
import type { DeviceInfo as BaseDeviceInfo } from '../lib/utils/serverDevice'

// Extended device info interface with client-side properties
export interface DeviceInfo extends BaseDeviceInfo {
    width: number
    height: number
    isMounted: boolean
    /**
     * Orientation of the device (portrait or landscape)
     */
    orientation: 'portrait' | 'landscape'
    /**
     * Whether the device has a pointer (mouse) or is touch-based
     */
    hasPointer: boolean
}

/**
 * Custom hook for detecting device type and screen dimensions
 * Uses react-responsive for media queries and handles SSR
 * Optimized with memoization to prevent unnecessary re-renders
 *
 * @param initialDeviceType - Optional initial device type for SSR
 * @returns Device information including type, dimensions, and mounting state
 *
 * @example
 * ```tsx
 * const { isMobile, isDesktop, deviceType } = useDevice();
 *
 * return (
 *   <div>
 *     {isMobile ? <MobileView /> : <DesktopView />}
 *   </div>
 * );
 * ```
 */
export function useDevice(initialDeviceType: DeviceType = DeviceType.Desktop): DeviceInfo {
    // Track if component is mounted to handle SSR
    const [isMounted, setIsMounted] = useState(false)

    // Use media queries with SSR support
    const isMobileQuery = useMediaQuery({ maxWidth: DEVICE_BREAKPOINTS.mobile })

    const isTabletQuery = useMediaQuery({
        minWidth: DEVICE_BREAKPOINTS.mobile + 1,
        maxWidth: DEVICE_BREAKPOINTS.tablet,
    })

    const isDesktopQuery = useMediaQuery({ minWidth: DEVICE_BREAKPOINTS.desktop })

    // Track window dimensions
    const [dimensions, setDimensions] = useState({
        width: 0,
        height: 0,
    })

    // Memoize the resize handler to prevent recreation on each render
    const handleResize = useCallback(() => {
        // Use requestAnimationFrame to throttle updates and improve performance
        requestAnimationFrame(() => {
            setDimensions({
                width: window.innerWidth,
                height: window.innerHeight,
            })
        })
    }, [])

    // Set mounted state and get initial dimensions
    useEffect(() => {
        setIsMounted(true)

        // Set initial dimensions
        setDimensions({
            width: window.innerWidth,
            height: window.innerHeight,
        })

        // Handle resize events with throttling
        window.addEventListener('resize', handleResize, { passive: true })
        return () => window.removeEventListener('resize', handleResize)
    }, [handleResize])

    // Determine device type based on media queries - memoized to prevent recalculation
    const deviceType = useMemo(() => {
        if (!isMounted) return initialDeviceType

        if (isMobileQuery) return DeviceType.Mobile
        if (isTabletQuery) return DeviceType.Tablet
        return DeviceType.Desktop
    }, [isMounted, isMobileQuery, isTabletQuery, initialDeviceType])

    // Determine if device has pointer - memoized
    const hasPointer = useMemo(() => {
        if (!isMounted) return true // Default to true for SSR
        return window.matchMedia('(pointer: fine)').matches
    }, [isMounted])

    // Determine orientation - memoized
    const orientation = useMemo(() => {
        if (!isMounted) return 'portrait' // Default to portrait for SSR
        return dimensions.width > dimensions.height ? 'landscape' : 'portrait'
    }, [isMounted, dimensions.width, dimensions.height])

    // Memoize the entire return object to prevent unnecessary re-renders
    return useMemo(
        () => ({
            isMobile: isMounted ? isMobileQuery : initialDeviceType === DeviceType.Mobile,
            isTablet: isMounted ? isTabletQuery : initialDeviceType === DeviceType.Tablet,
            isDesktop: isMounted ? isDesktopQuery : initialDeviceType === DeviceType.Desktop,
            deviceType,
            width: dimensions.width,
            height: dimensions.height,
            isMounted,
            orientation,
            hasPointer,
        }),
        [
            isMounted,
            isMobileQuery,
            isTabletQuery,
            isDesktopQuery,
            initialDeviceType,
            deviceType,
            dimensions.width,
            dimensions.height,
            orientation,
            hasPointer,
        ],
    )
}
