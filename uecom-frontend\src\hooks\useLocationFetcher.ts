'use client'

import { useState, useEffect, useCallback } from 'react'
import { Coordinates, GeocodingResult, LocationDetails } from '../types/location'
import { parseGeocodingResult } from '@/lib/utils/geocoding'

interface UseLocationFetcherResult {
    loading: boolean
    locationDetails: LocationDetails | null
    error: string | null
    refetch: () => void
}

/**
 * Custom hook for fetching user's location and converting to address
 * @param minLoadingTime Minimum time in ms to show loading state (default: 1500ms)
 */
const useLocationFetcher = (minLoadingTime = 1500): UseLocationFetcherResult => {
    const [loading, setLoading] = useState<boolean>(true)
    const [locationDetails, setLocationDetails] = useState<LocationDetails | null>(null)
    const [error, setError] = useState<string | null>(null)
    const [retryCount, setRetryCount] = useState<number>(0)
    const [fetchTrigger, setFetchTrigger] = useState<number>(0)

    const MAX_RETRIES = 3

    // Memoize the refetch function to prevent unnecessary re-renders
    const refetch = useCallback(() => {
        setLoading(true)
        setError(null)
        setRetryCount(0)
        setFetchTrigger((prev) => prev + 1)
    }, [])

    useEffect(() => {
        let isMounted = true

        const fetchLocation = async () => {
            try {
                // Record the start time
                const startTime = Date.now()

                // Get user's current position with high accuracy settings
                const position: GeolocationPosition = await new Promise((resolve, reject) => {
                    navigator.geolocation.getCurrentPosition(resolve, reject, {
                        enableHighAccuracy: true,
                        timeout: 15000,
                        maximumAge: 0,
                    })
                })

                if (!isMounted) return

                const coords: Coordinates = {
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude,
                    accuracy: position.coords.accuracy,
                }

                // Use our server-side API route to get detailed address information
                // This protects our API key from being exposed in client-side code
                const response = await fetch(
                    `/api/geocode?latlng=${coords.latitude},${coords.longitude}&result_type=street_address|route|neighborhood|locality&language=en`,
                )

                if (!isMounted) return

                const data = await response.json()

                if (data.status === 'OK' && data.results && data.results.length > 0) {
                    // Get the most detailed result (usually the first one)
                    const result: GeocodingResult = data.results[0]
                    const details = parseGeocodingResult(result, coords)

                    if (isMounted) {
                        setLocationDetails(details)
                    }
                } else if (retryCount < MAX_RETRIES) {
                    // Retry with different parameters if we didn't get good results
                    if (isMounted) {
                        setRetryCount((prev) => prev + 1)
                    }
                    throw new Error('Insufficient location data, retrying...')
                } else {
                    if (isMounted) {
                        setError('Unable to retrieve detailed address information')
                    }
                }

                // Calculate how much time has passed
                const elapsedTime = Date.now() - startTime
                const remainingTime = Math.max(0, minLoadingTime - elapsedTime)

                // Wait for the remaining time to ensure loader shows for at least the minimum time
                setTimeout(() => {
                    if (isMounted) {
                        setLoading(false)
                    }
                }, remainingTime)
            } catch (err) {
                console.error('Error fetching location:', err)

                if (isMounted && retryCount < MAX_RETRIES) {
                    // Retry the location fetch
                    setRetryCount((prev) => prev + 1)
                    console.log(`Retrying location fetch (${retryCount + 1}/${MAX_RETRIES})...`)

                    // Wait a bit before retrying
                    setTimeout(() => {
                        if (isMounted) {
                            fetchLocation()
                        }
                    }, 1000)

                    return
                }

                if (isMounted) {
                    setError(
                        'Error fetching location. Please make sure location services are enabled and try again.',
                    )

                    // Even on error, wait for a total of the minimum loading time before hiding the loader
                    setTimeout(() => {
                        if (isMounted) {
                            setLoading(false)
                        }
                    }, minLoadingTime)
                }
            }
        }

        fetchLocation()

        // Cleanup function to prevent state updates after unmount
        return () => {
            isMounted = false
        }
    }, [retryCount, fetchTrigger, minLoadingTime])

    return { loading, locationDetails, error, refetch }
}

export default useLocationFetcher
