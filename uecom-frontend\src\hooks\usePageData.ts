'use client'

import { useState, useEffect, useCallback } from 'react'
import { PageData } from '@/types'

interface UsePageDataResult {
    pageData: PageData | null
    loading: boolean
    error: string | null
    refetch: () => void
}

/**
 * Custom hook for fetching page data from the API
 *
 * @param pageName - Name of the page to fetch data for
 * @returns Object containing page data, loading state, error state, and refetch function
 *
 * @example
 * ```tsx
 * const { pageData, loading, error, refetch } = usePageData('home');
 *
 * if (loading) return <LoadingSpinner />;
 * if (error) return <ErrorMessage message={error} />;
 * if (!pageData) return <EmptyState />;
 *
 * return <PageRenderer data={pageData} />;
 * ```
 */
const usePageData = (pageName: string): UsePageDataResult => {
    const [pageData, setPageData] = useState<PageData | null>(null)
    const [loading, setLoading] = useState<boolean>(true)
    const [error, setError] = useState<string | null>(null)
    const [fetchTrigger, setFetchTrigger] = useState<number>(0)

    // Memoize the refetch function to prevent unnecessary re-renders
    const refetch = useCallback(() => {
        setLoading(true)
        setError(null)
        setFetchTrigger((prev) => prev + 1)
    }, [])

    useEffect(() => {
        let isMounted = true

        const fetchPageData = async () => {
            try {
                setLoading(true)

                // Use a default URL if the environment variable is not set
                const baseUrl = process.env.NEXT_PUBLIC_APP_SERVICE_URL || 'http://localhost:8000'
                const apiUrl = `${baseUrl}/api/page-data/${pageName}`

                console.log(`Fetching page data from: ${apiUrl}`)

                // Fetch page data from API
                const response = await fetch(apiUrl)

                if (!response.ok) {
                    throw new Error(`Failed to fetch page data: ${response.statusText}`)
                }

                const data = await response.json()

                if (isMounted) {
                    setPageData(data)
                    setError(null)
                }
            } catch (err) {
                console.error('Error fetching page data:', err)

                if (isMounted) {
                    setError('Failed to load page data. Please try again.')
                }
            } finally {
                if (isMounted) {
                    setLoading(false)
                }
            }
        }

        fetchPageData()

        // Cleanup function to prevent state updates after unmount
        return () => {
            isMounted = false
        }
    }, [pageName, fetchTrigger])

    return { pageData, loading, error, refetch }
}

export default usePageData
