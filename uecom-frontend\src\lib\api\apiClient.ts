/**
 * API client for making HTTP requests
 * Provides a consistent interface for making API calls
 */

// Define request options type
export interface RequestOptions {
    headers?: Record<string, string>
    cache?: RequestCache
    next?: {
        revalidate?: number | false
        tags?: string[]
    }
}

// Define API response type
export interface ApiResponse<T> {
    data: T | null
    error: string | null
    status: number
}

/**
 * Fetch data from API with error handling and typing
 *
 * @param url - URL to fetch data from
 * @param options - Request options
 * @returns Promise resolving to typed API response
 *
 * @example
 * ```ts
 * const { data, error } = await fetchData<User[]>('/api/users');
 *
 * if (error) {
 *   console.error(error);
 *   return null;
 * }
 *
 * return data;
 * ```
 */
export async function fetchData<T>(url: string, options?: RequestOptions): Promise<ApiResponse<T>> {
    try {
        // Set default headers
        const headers = {
            'Content-Type': 'application/json',
            ...options?.headers,
        }

        // Make request
        const response = await fetch(url, {
            headers,
            cache: options?.cache || 'default',
            next: options?.next,
        })

        // Check if response is OK
        if (!response.ok) {
            // Handle error response
            let errorMessage: string

            try {
                // Try to parse error message from response
                const errorData = await response.json()
                errorMessage =
                    errorData.message || `Error: ${response.status} ${response.statusText}`
            } catch {
                // If parsing fails, use status text
                errorMessage = `Error: ${response.status} ${response.statusText}`
            }

            return {
                data: null,
                error: errorMessage,
                status: response.status,
            }
        }

        // Parse response data
        const data = await response.json()

        // Return successful response
        return {
            data,
            error: null,
            status: response.status,
        }
    } catch (error) {
        // Handle fetch errors (network issues, etc.)
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'

        return {
            data: null,
            error: errorMessage,
            status: 500,
        }
    }
}

/**
 * Post data to API with error handling and typing
 *
 * @param url - URL to post data to
 * @param data - Data to post
 * @param options - Request options
 * @returns Promise resolving to typed API response
 *
 * @example
 * ```ts
 * const newUser = { name: 'John', email: '<EMAIL>' };
 * const { data, error } = await postData<User>('/api/users', newUser);
 *
 * if (error) {
 *   console.error(error);
 *   return null;
 * }
 *
 * return data;
 * ```
 */
export async function postData<T>(
    url: string,
    data: any,
    options?: RequestOptions,
): Promise<ApiResponse<T>> {
    try {
        // Set default headers
        const headers = {
            'Content-Type': 'application/json',
            ...options?.headers,
        }

        // Make request
        const response = await fetch(url, {
            method: 'POST',
            headers,
            body: JSON.stringify(data),
            cache: options?.cache || 'no-store',
            next: options?.next,
        })

        // Check if response is OK
        if (!response.ok) {
            // Handle error response
            let errorMessage: string

            try {
                // Try to parse error message from response
                const errorData = await response.json()
                errorMessage =
                    errorData.message || `Error: ${response.status} ${response.statusText}`
            } catch {
                // If parsing fails, use status text
                errorMessage = `Error: ${response.status} ${response.statusText}`
            }

            return {
                data: null,
                error: errorMessage,
                status: response.status,
            }
        }

        // Parse response data
        const responseData = await response.json()

        // Return successful response
        return {
            data: responseData,
            error: null,
            status: response.status,
        }
    } catch (error) {
        // Handle fetch errors (network issues, etc.)
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'

        return {
            data: null,
            error: errorMessage,
            status: 500,
        }
    }
}
