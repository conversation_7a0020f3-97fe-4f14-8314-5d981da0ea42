import { AddressComponent, Coordinates, GeocodingResult, LocationDetails } from '@/types/location'

/**
 * Extracts a specific address component from the address components array
 */
export const getAddressComponent = (
    components: AddressComponent[],
    type: string,
): string | undefined => {
    const component = components.find((comp) => comp.types.includes(type))
    return component?.long_name
}

/**
 * Parses a geocoding result into a structured LocationDetails object
 */
export const parseGeocodingResult = (
    result: GeocodingResult,
    coords: Coordinates,
): LocationDetails => {
    const components = result.address_components

    return {
        formattedAddress: result.formatted_address,
        streetNumber: getAddressComponent(components, 'street_number'),
        route: getAddressComponent(components, 'route'),
        neighborhood: getAddressComponent(components, 'neighborhood'),
        locality: getAddressComponent(components, 'locality'),
        administrativeArea: getAddressComponent(components, 'administrative_area_level_1'),
        country: getAddressComponent(components, 'country'),
        postalCode: getAddressComponent(components, 'postal_code'),
        coordinates: coords,
    }
}
