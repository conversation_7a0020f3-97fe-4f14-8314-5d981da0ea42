import axios from 'axios'
import { doGetCall, doPostCall, doPutCall, doDeleteCall } from './http-utils'
import { handleError } from './response-utils'

jest.mock('axios')
const mockedAxios = axios as jest.Mocked<typeof axios>

global.console.error = jest.fn()

describe('HTTP Utility Functions Tests', () => {
    beforeEach(() => {
        jest.clearAllMocks()
    })

    test('should make a successful GET request', async () => {
        const mockResponse = { data: { message: 'Success' } }
        mockedAxios.get.mockResolvedValue(mockResponse)

        const result = await doGetCall('http://test.com')

        expect(result).toEqual(mockResponse.data)
        expect(mockedAxios.get).toHaveBeenCalledWith('http://test.com', expect.any(Object))
    })

    test('should handle GET request error', async () => {
        const mockError = new Error('Client error')
        mockedAxios.get.mockRejectedValue(mockError)

        const result = await doGetCall('http://test.com')

        expect(result).toBeUndefined()
        expect(handleError).toHaveBeenCalledWith(mockError, 'http://test.com')
    })

    test('should make a successful POST request', async () => {
        const mockResponse = { data: { message: 'Success' } }
        mockedAxios.post.mockResolvedValue(mockResponse)

        const result = await doPostCall('http://test.com', { data: 'test' })

        expect(result).toEqual(mockResponse.data)
        expect(mockedAxios.post).toHaveBeenCalledWith(
            'http://test.com',
            { data: 'test' },
            expect.any(Object),
        )
    })

    test('should handle POST request error', async () => {
        const mockError = new Error('Server error')
        mockedAxios.post.mockRejectedValue(mockError)

        const result = await doPostCall('http://test.com', { data: 'test' })

        expect(result).toBeUndefined()
        expect(handleError).toHaveBeenCalledWith(mockError, 'http://test.com')
    })

    test('should make a successful PUT request', async () => {
        const mockResponse = { data: { message: 'Updated successfully' } }
        mockedAxios.put.mockResolvedValue(mockResponse)

        const result = await doPutCall('http://test.com', { data: 'test' })

        expect(result).toEqual(mockResponse.data)
        expect(mockedAxios.put).toHaveBeenCalledWith(
            'http://test.com',
            { data: 'test' },
            expect.any(Object),
        )
    })

    test('should handle PUT request error', async () => {
        const mockError = new Error('Server error')
        mockedAxios.put.mockRejectedValue(mockError)

        const result = await doPutCall('http://test.com', { data: 'test' })

        expect(result).toBeUndefined()
        expect(handleError).toHaveBeenCalledWith(mockError, 'http://test.com')
    })

    test('should make a successful DELETE request', async () => {
        const mockResponse = { data: { message: 'Deleted successfully' } }
        mockedAxios.delete.mockResolvedValue(mockResponse)

        const result = await doDeleteCall('http://test.com')

        expect(result).toEqual(mockResponse.data)
        expect(mockedAxios.delete).toHaveBeenCalledWith('http://test.com', expect.any(Object))
    })

    test('should handle DELETE request error', async () => {
        const mockError = new Error('Client error')
        mockedAxios.delete.mockRejectedValue(mockError)

        const result = await doDeleteCall('http://test.com')

        expect(result).toBeUndefined()
        expect(handleError).toHaveBeenCalledWith(mockError, 'http://test.com')
    })
})
