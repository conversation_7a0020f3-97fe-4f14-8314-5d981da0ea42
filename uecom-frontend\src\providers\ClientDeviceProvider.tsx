'use client'

import { useEffect, useState } from 'react'
import { <PERSON>ceProvider } from './DeviceProvider'
import { DeviceType } from '../lib/constants/breakpoints'

interface ClientDeviceProviderProps {
    children: React.ReactNode
    initialDeviceType?: DeviceType
}

/**
 * Client-side device provider that handles device detection
 * This component is used to wrap the application and provide device context
 * It handles hydration issues by waiting for client-side mounting
 *
 * @param children - Child components
 * @param initialDeviceType - Optional initial device type from SSR
 *
 * @example
 * ```tsx
 * <ClientDeviceProvider initialDeviceType={deviceType}>
 *   <App />
 * </ClientDeviceProvider>
 * ```
 */
export default function ClientDeviceProvider({
    children,
    initialDeviceType = DeviceType.Desktop,
}: ClientDeviceProviderProps) {
    // Track if component is mounted to handle hydration
    const [isMounted, setIsMounted] = useState(false)

    // Detect device type on client side
    useEffect(() => {
        // Mark as mounted to avoid hydration issues
        setIsMounted(true)
    }, [])

    // If not mounted yet, render children without device context to avoid hydration mismatch
    if (!isMounted) {
        return <>{children}</>
    }

    // Once mounted, provide device context
    return <DeviceProvider initialDeviceType={initialDeviceType}>{children}</DeviceProvider>
}
