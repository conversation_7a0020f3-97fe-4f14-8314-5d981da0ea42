'use client'

import LocationDisplay from '@/components/Location/LocationDisplay'
import LocationLoader from '@/components/Location/LocationLoader'
import useLocationFetcher from '@/hooks/useLocationFetcher'
import { createContext, ReactNode, useContext, useState, useEffect } from 'react'
import { LocationDetails } from '@/types/location'
import { useAuth } from '@/hooks/useAuth'

interface LocationLoaderContextType {
    locationDetails: LocationDetails | null
    loading: boolean
    error: string | null
    refetch: () => void
}

const LocationLoaderContext = createContext<LocationLoaderContextType | undefined>(undefined)

export const useLocationLoader = () => {
    const context = useContext(LocationLoaderContext)
    if (context === undefined) {
        throw new Error('useLocationLoader must be used within a LocationLoaderProvider')
    }
    return context
}

interface LocationLoaderProviderProps {
    children: ReactNode
}

export function LocationLoaderProvider({ children }: LocationLoaderProviderProps) {
    const { isAuthenticated } = useAuth()
    const { locationDetails, loading, error, refetch } = useLocationFetcher()
    const [showMainApp, setShowMainApp] = useState(false)
    const [showLocationDisplay, setShowLocationDisplay] = useState(false)

    useEffect(() => {
        if (!loading && locationDetails) {
            const timer = setTimeout(() => {
                setShowMainApp(true)
            }, 2000)

            return () => clearTimeout(timer)
        }
    }, [loading, locationDetails])

    useEffect(() => {
        setShowLocationDisplay(isAuthenticated)
    }, [isAuthenticated])

    if (showLocationDisplay && loading) {
        return <LocationLoader />
    }

    if (showLocationDisplay && !showMainApp && locationDetails) {
        return (
            <LocationDisplay
                address={locationDetails?.formattedAddress || 'Address not available'}
                postalCode={locationDetails?.postalCode}
            />
        )
    }
    return (
        <LocationLoaderContext.Provider
            value={{
                locationDetails,
                loading,
                error,
                refetch,
            }}
        >
            {children}
        </LocationLoaderContext.Provider>
    )
}
