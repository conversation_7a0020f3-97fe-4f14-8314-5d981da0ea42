'use client'

import { createContext, ReactNode, useContext, useState } from 'react'

interface LocationContextType {
    isLocationOpen: boolean
    openLocation: () => void
    closeLocation: () => void
}

const LocationContext = createContext<LocationContextType | undefined>(undefined)

export const useLocation = () => {
    const context = useContext(LocationContext)
    if (context === undefined) {
        throw new Error('useLocation must be used within a LocationProvider')
    }
    return context
}

interface LocationProviderProps {
    children: ReactNode
}

export function LocationProvider({ children }: LocationProviderProps) {
    const [isLocationOpen, setIsLocationOpen] = useState(false)

    const openLocation = () => setIsLocationOpen(true)
    const closeLocation = () => setIsLocationOpen(false)

    return (
        <LocationContext.Provider
            value={{
                isLocationOpen,
                openLocation,
                closeLocation,
            }}
        >
            {children}
        </LocationContext.Provider>
    )
}
