'use client'

import { createContext, ReactNode, useContext, useState } from 'react'

interface SearchContextType {
    isSearchOpen: boolean
    openSearch: () => void
    closeSearch: () => void
    toggleSearch: () => void
}

const SearchContext = createContext<SearchContextType | undefined>(undefined)

export const useSearch = () => {
    const context = useContext(SearchContext)
    if (context === undefined) {
        throw new Error('useSearch must be used within a SearchProvider')
    }
    return context
}

interface SearchProviderProps {
    children: ReactNode
}

export function SearchProvider({ children }: SearchProviderProps) {
    const [isSearchOpen, setIsSearchOpen] = useState(false)

    const openSearch = () => setIsSearchOpen(true)
    const closeSearch = () => setIsSearchOpen(false)
    const toggleSearch = () => setIsSearchOpen((prev) => !prev)

    return (
        <SearchContext.Provider
            value={{
                isSearchOpen,
                openSearch,
                closeSearch,
                toggleSearch,
            }}
        >
            {children}
        </SearchContext.Provider>
    )
}
