/**
 * Authentication Store
 *
 * This module implements the Observer pattern for managing authentication state.
 * It uses Zustand for state management and provides a clean API for authentication operations.
 *
 * @module stores/auth/auth-store
 */

import { create } from 'zustand'
import { requestOTP, verifyOTP, resendOTP } from '@/app/api/auth'

/**
 * Authentication state interface
 */
interface AuthState {
    // State
    isAuthenticated: boolean
    isLoading: boolean
    mobile: string
    error: string | null

    // Actions
    setMobile: (mobile: string) => void
    requestOtp: (mobile: string) => Promise<boolean>
    verifyOtp: (mobile: string, otp: string) => Promise<boolean>
    resendOtp: () => Promise<boolean>
    logout: () => void
    clearError: () => void
}

/**
 * Authentication store using Zustand
 * Implements the Observer pattern for state management
 */
export const useAuthStore = create<AuthState>((set, get) => ({
    // Initial state
    isAuthenticated: false,
    isLoading: false,
    mobile: '',
    error: null,

    // Actions
    setMobile: (mobile: string) => set({ mobile }),

    /**
     * Request an OTP for the provided mobile number
     *
     * @param {string} mobile - The mobile number
     * @returns {Promise<boolean>} True if the request was successful
     */
    requestOtp: async (mobile: string) => {
        try {
            set({ isLoading: true, error: null, mobile })

            const response = await requestOTP(mobile)

            // Check if the response indicates success (status code 200-299)
            const isSuccess = response.statusCode >= 200 && response.statusCode < 300

            if (!isSuccess) {
                set({
                    isLoading: false,
                    error: response.error?.message || response.message || 'Failed to send OTP',
                })
                return false
            }

            set({ isLoading: false })
            return true
        } catch (error) {
            set({
                isLoading: false,
                error: error instanceof Error ? error.message : 'An unknown error occurred',
            })
            return false
        }
    },

    /**
     * Verify an OTP for the current mobile number
     *
     * @param {string} mobile - The mobile number
     * @param {string} otp - The OTP to verify
     * @returns {Promise<boolean>} True if the verification was successful
     */
    verifyOtp: async (mobile: string, otp: string) => {
        try {
            set({ isLoading: true, error: null })

            const response = await verifyOTP(mobile, otp)

            // Check if the response indicates success (status code 200-299)
            const isSuccess = response.statusCode >= 200 && response.statusCode < 300

            if (!isSuccess) {
                set({
                    isLoading: false,
                    error: response.error?.message || response.message || 'Invalid OTP',
                })
                return false
            }

            // Store authentication token if provided
            if (response.data?.tokens?.accessToken) {
                localStorage.setItem('authToken', response.data.tokens.accessToken)
            }

            set({ isLoading: false, isAuthenticated: true })
            return true
        } catch (error) {
            set({
                isLoading: false,
                error: error instanceof Error ? error.message : 'An unknown error occurred',
            })
            return false
        }
    },

    /**
     * Resend an OTP to the current mobile number
     *
     * @returns {Promise<boolean>} True if the resend was successful
     */
    resendOtp: async () => {
        try {
            const { mobile } = get()

            if (!mobile) {
                set({ error: 'No mobile number provided' })
                return false
            }

            set({ isLoading: true, error: null })

            const response = await resendOTP(mobile)

            // Check if the response indicates success (status code 200-299)
            const isSuccess = response.statusCode >= 200 && response.statusCode < 300

            if (!isSuccess) {
                set({
                    isLoading: false,
                    error: response.error?.message || response.message || 'Failed to resend OTP',
                })
                return false
            }

            set({ isLoading: false })
            return true
        } catch (error) {
            set({
                isLoading: false,
                error: error instanceof Error ? error.message : 'An unknown error occurred',
            })
            return false
        }
    },

    /**
     * Log out the current user
     */
    logout: () => {
        localStorage.removeItem('authToken')
        set({ isAuthenticated: false, mobile: '' })
    },

    /**
     * Clear any error messages
     */
    clearError: () => set({ error: null }),
}))
