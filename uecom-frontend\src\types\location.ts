// Types related to location data and components

export interface Coordinates {
    latitude: number
    longitude: number
    accuracy: number
}

export interface LocationDetails {
    formattedAddress: string
    streetNumber?: string
    route?: string
    neighborhood?: string
    locality?: string
    administrativeArea?: string
    country?: string
    postalCode?: string
    coordinates: Coordinates
}

export interface AddressComponent {
    long_name: string
    short_name: string
    types: string[]
}

export interface GeocodingResult {
    formatted_address: string
    address_components: AddressComponent[]
    geometry: {
        location: {
            lat: number
            lng: number
        }
        location_type: string
    }
}

export interface LocationDisplayProps {
    address: string
    postalCode?: string
}

export interface LocationIconProps {
    animate?: boolean
    size?: 'sm' | 'md' | 'lg'
}

export interface LocationErrorProps {
    message: string
    onRetry?: () => void
}
